import type { Config, Context } from "@netlify/edge-functions";

// The Netlify Edge runtime provides a global `Netlify` object at runtime, but the
// TypeScript compiler is unaware of it. We declare its shape here so that
// `strict` compilation won’t fail.
declare const Netlify: {
  env: {
    get(name: string): string | undefined;
  };
};

// Edge function doesn't support FormData the same way, so we'll use a different approach
export default async (request: Request, context: Context) => {
  try {
    const body = await request.json();
    const { 
      plainText, 
      templateHtml, 
      templateId,
      letterId,
      userId 
    } = body;

    if (!plainText || !templateHtml) {
      return new Response(
        JSON.stringify({ error: 'Plain text and template HTML are required' }),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Create system and user prompts
    const systemPrompt = `
You are a professional job application letter designer. Your task is to integrate the provided plain text letter into the given HTML template.

Instructions:
1. Integrate the entire plain text letter content into the HTML template.
2. Format the plain text content appropriately using HTML paragraphs and other suitable tags, preserving all original text and structure.
3. DO NOT change the font size of the template.
4. Return ONLY the complete HTML document with the letter integrated and without any comments.
5. Provide your response as a JSON object with an "html" property containing the complete HTML document.

DO NOT alter, summarize, or modify the plain text letter content. Include ALL parts exactly as written.

Your response MUST be valid JSON with the following structure: { "html": "your complete HTML document here" }
    `;

    const userPrompt = `Plain text letter:\n${plainText}\n\nHTML template:\n${templateHtml}`;

    // Make streaming request to OpenAI
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${Netlify.env.get("NEXT_PUBLIC_OPENAI_API_KEY")}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: userPrompt
          }
        ],
        response_format: {
          type: "json_object"
        },
        temperature: 0,
        max_tokens: 8192,
        top_p: 0,
        stream: true,
      }),
    });

    if (!openaiResponse.ok) {
      throw new Error(`OpenAI API error: ${openaiResponse.statusText}`);
    }

    // Create a ReadableStream to handle the OpenAI streaming response
    const encoder = new TextEncoder();
    const decoder = new TextDecoder();
    
    let fullContent = '';
    
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openaiResponse.body!.getReader();
        
        try {
          let buffer = '';
          
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            
            for (const line of lines) {
              if (line.trim() === '') continue;
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                
                if (data === '[DONE]') {
                  // Parse the complete response
                  let parsedHtml = '';
                  try {
                    const jsonResponse = JSON.parse(fullContent);
                    parsedHtml = jsonResponse.html || '';
                  } catch (parseError) {
                    parsedHtml = fullContent;
                  }
                  
                  // Send the final complete response
                  const finalData = JSON.stringify({
                    type: 'complete',
                    html: parsedHtml,
                    letterId,
                    userId,
                    templateId
                  });
                  controller.enqueue(encoder.encode(`data: ${finalData}\n\n`));
                  controller.enqueue(encoder.encode('data: [DONE]\n\n'));
                  controller.close();
                  return;
                }
                
                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    fullContent += content;
                    // Send chunk to client
                    const chunkData = JSON.stringify({
                      type: 'chunk',
                      text: content,
                      letterId,
                      userId
                    });
                    controller.enqueue(encoder.encode(`data: ${chunkData}\n\n`));
                  }
                } catch (e) {
                  // Ignore parse errors for incomplete JSON
                }
              }
            }
          }
        } catch (error) {
          const errorData = JSON.stringify({
            type: 'error',
            error: error instanceof Error ? error.message : 'Stream processing error'
          });
          controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
          controller.enqueue(encoder.encode('data: [DONE]\n\n'));
          controller.close();
        }
      }
    });

    // Return streaming response
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'X-Accel-Buffering': 'no', // Disable Nginx buffering
      },
    });
    
  } catch (error) {
    console.error('Error in Edge Function:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to process request'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}

export const config: Config = {
  path: "/api/edge/generate-application-letter"
};