import type { Config, Context } from "@netlify/edge-functions";

// The Netlify Edge runtime provides a global `Netlify` object at runtime, but the
// TypeScript compiler is unaware of it. We declare its shape here so that
// `strict` compilation won't fail.
declare const Netlify: {
  env: {
    get(name: string): string | undefined;
  };
};

interface StreamEvent {
  type: 'status' | 'plain-text-chunk' | 'template-chunk' | 'complete' | 'error';
  phase?: 'plain-text' | 'template';
  content?: string;
  progress?: number;
  metadata?: any;
}

interface RequestBody {
  resumeData: string; // Base64 encoded or extracted text
  resumeMimeType: string;
  jobDescription?: string;
  jobImage?: string; // Base64 encoded
  templateId: string;
  templateHtml?: string;
  userId?: string;
  letterId?: string;
  isEdit?: boolean;
}

/**
 * Streams Gemini API response and processes chunks
 */
async function streamGeminiResponse(
  response: Response,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder,
  onTextChunk: (text: string) => void
): Promise<void> {
  const reader = response.body!.getReader();
  const decoder = new TextDecoder();
  let buffer = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        
        // Handle Gemini streaming format (NDJSON - newline-delimited JSON)
        try {
          const parsed = JSON.parse(line);
          const candidates = parsed.candidates;
          
          if (candidates && candidates[0] && candidates[0].content && candidates[0].content.parts) {
            const text = candidates[0].content.parts[0].text;
            if (text) {
              onTextChunk(text);
              
              // Send chunk to client
              const chunkEvent: StreamEvent = {
                type: 'plain-text-chunk',
                phase: 'plain-text',
                content: text
              };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkEvent)}\n\n`));
            }
          }
        } catch (e) {
          // Ignore parse errors for incomplete JSON
          console.warn('Failed to parse Gemini chunk:', e);
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}

/**
 * Streams OpenAI API response and processes chunks
 */
async function streamOpenAIResponse(
  response: Response,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder,
  onHtmlUpdate: (html: string) => void
): Promise<void> {
  const reader = response.body!.getReader();
  const decoder = new TextDecoder();
  let buffer = '';
  let fullContent = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            // Parse the complete response
            let parsedHtml = '';
            try {
              const jsonResponse = JSON.parse(fullContent);
              parsedHtml = jsonResponse.html || '';
            } catch (parseError) {
              parsedHtml = fullContent;
            }
            onHtmlUpdate(parsedHtml);
            return;
          }

          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices?.[0]?.delta?.content;
            if (content) {
              fullContent += content;
              
              // Send chunk to client
              const chunkEvent: StreamEvent = {
                type: 'template-chunk',
                phase: 'template',
                content: content
              };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkEvent)}\n\n`));
            }
          } catch (e) {
            // Ignore parse errors for incomplete JSON
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}

/**
 * Prepares Gemini API request for plain text generation
 */
function prepareGeminiRequest(body: RequestBody): any {
  const date = new Date().toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    timeZone: 'Asia/Jakarta',
  });

  const prompt = `
You are a professional job application letter writer. You will receive a resume and a job description with a specific job position title. Craft a plain-text job application letter (surat lamaran pekerjaan) in Bahasa Indonesia that is formal. Do not include any markdown, bullet points, or headings; write only the letter text ready to copy-paste.

First, analyze the resume and job description thoroughly to identify:
1. Skills EXPLICITLY mentioned in the resume that match the job requirements
2. Skills IMPLICITLY suggested by the resume
3. Related skills in the resume that could TRANSFER to the job requirements
4. Education, certifications, and relevant experience from the resume

Then, write a formal application letter that:
- Uses proper Indonesian business letter format
- The character count of the entire letter (including spaces) should be around 1300-1600 characters, not more, not less
- Includes current date (${date}) at the top
- Addresses the HR department of the company. If the company's address is known, include it; otherwise, omit the address line.
- States the purpose of applying for the specific position clearly
- Highlights the candidate's relevant education, skills, and experiences
- Shows enthusiasm for the position and desire to contribute to the company
- Includes a polite closing asking for interview opportunity
- Uses a formal closing salutation

Job Information: ${body.jobDescription ? body.jobDescription : '[A job posting image is provided. Please analyze it to identify the job requirements, responsibilities, and qualifications]'}

Guidelines:
1. Use a formal and respectful tone appropriate for Indonesian business correspondence.
2. Include proper letter components IN ORDER: date, subject line (Perihal: Lamaran Pekerjaan sebagai [position]), recipient, body, and closing.
3. Be reasonable about implied skills from the resume.
4. EXTREMELY IMPORTANT: NEVER use phrases like "belum memiliki pengalaman" or mention any lack of experience.
5. Focus ONLY on the candidate's strengths and relevant experience.
6. If there are significant skill gaps, express enthusiasm for continuing professional development.
7. Reference the attached resume/CV for more detailed information.
8. Follow standard Indonesian formal letter structure.
9. The ENTIRE letter (including spaces) should be around 1300-1600 characters, not more, not less. Make it concise, but still formal and complete.

Return only the plain-text application letter without any additional commentary or manual edits.
  `;

  const parts: any[] = [{ text: prompt }];

  // Add resume data
  if (body.resumeMimeType === 'text/plain') {
    parts.push({ text: `Resume Content:\n${body.resumeData}` });
  } else {
    parts.push({
      inlineData: {
        data: body.resumeData,
        mimeType: body.resumeMimeType
      }
    });
  }

  // Add job image if provided
  if (body.jobImage) {
    parts.push({
      inlineData: {
        data: body.jobImage,
        mimeType: 'image/jpeg' // Assume JPEG for now
      }
    });
  }

  return {
    contents: [{
      role: "user",
      parts: parts
    }],
    generationConfig: {
      temperature: 0.2,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 2048,
    }
  };
}

/**
 * Prepares OpenAI API request for template generation
 */
function prepareOpenAIRequest(plainText: string, templateHtml: string): any {
  const systemPrompt = `
You are a professional job application letter designer. Your task is to integrate the provided plain text letter into the given HTML template.

Instructions:
1. Integrate the entire plain text letter content into the HTML template.
2. Format the plain text content appropriately using HTML paragraphs and other suitable tags, preserving all original text and structure.
3. DO NOT change the font size of the template.
4. Return ONLY the complete HTML document with the letter integrated and without any comments.
5. Provide your response as a JSON object with an "html" property containing the complete HTML document.

DO NOT alter, summarize, or modify the plain text letter content. Include ALL parts exactly as written.

Your response MUST be valid JSON with the following structure: { "html": "your complete HTML document here" }
  `;

  const userPrompt = `Plain text letter:\n${plainText}\n\nHTML template:\n${templateHtml}`;

  return {
    model: "gpt-4o-mini",
    messages: [
      {
        role: "system",
        content: systemPrompt
      },
      {
        role: "user",
        content: userPrompt
      }
    ],
    response_format: {
      type: "json_object"
    },
    temperature: 0,
    max_tokens: 8192,
    top_p: 0,
    stream: true,
  };
}

export default async (request: Request, context: Context) => {
  try {
    const body: RequestBody = await request.json();
    const { 
      resumeData, 
      resumeMimeType,
      jobDescription, 
      jobImage,
      templateId,
      templateHtml,
      userId,
      letterId,
      isEdit
    } = body;

    if (!resumeData) {
      return new Response(
        JSON.stringify({ error: 'Resume data is required' }),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!jobDescription && !jobImage) {
      return new Response(
        JSON.stringify({ error: 'Either job description or job image is required' }),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const encoder = new TextEncoder();
    let plainTextComplete = '';
    let templateHtmlComplete = '';

    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Phase 1: Generate Plain Text with Gemini
          const statusEvent: StreamEvent = {
            type: 'status',
            phase: 'plain-text',
            content: 'Membuat teks surat...'
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(statusEvent)}\n\n`));

          // Make request to Gemini API (non-streaming)
          const geminiRequest = prepareGeminiRequest(body);
          const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${Netlify.env.get("NEXT_PUBLIC_GOOGLE_AI_API_KEY")}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(geminiRequest),
          });

          if (!geminiResponse.ok) {
            throw new Error(`Gemini API error: ${geminiResponse.statusText}`);
          }

          // Parse Gemini response and simulate streaming
          const geminiData = await geminiResponse.json();
          const fullText = geminiData.candidates?.[0]?.content?.parts?.[0]?.text || '';
          plainTextComplete = fullText;

          // Send complete text at once
          if (fullText) {
            const chunkEvent: StreamEvent = {
              type: 'plain-text-chunk',
              phase: 'plain-text',
              content: fullText
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkEvent)}\n\n`));
          }

          // Phase 2: Generate Template (if template selected)
          if (templateHtml) {
            const templateStatusEvent: StreamEvent = {
              type: 'status',
              phase: 'template',
              content: 'Membuat desain surat...'
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(templateStatusEvent)}\n\n`));

            // Make request to OpenAI API
            const openaiRequest = prepareOpenAIRequest(plainTextComplete, templateHtml);
            const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${Netlify.env.get("NEXT_PUBLIC_OPENAI_API_KEY")}`,
              },
              body: JSON.stringify(openaiRequest),
            });

            if (!openaiResponse.ok) {
              throw new Error(`OpenAI API error: ${openaiResponse.statusText}`);
            }

            // Stream OpenAI response
            await streamOpenAIResponse(openaiResponse, controller, encoder, (html) => {
              templateHtmlComplete = html;
            });
          }

          // Send completion event
          const completeEvent: StreamEvent = {
            type: 'complete',
            content: 'Letter generation completed successfully',
            metadata: {
              plainText: plainTextComplete,
              templateHtml: templateHtmlComplete || null,
              templateId,
              letterId,
              userId,
              isEdit,
              timestamp: new Date().toISOString()
            }
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(completeEvent)}\n\n`));
          controller.enqueue(encoder.encode('data: [DONE]\n\n'));
          controller.close();

        } catch (error) {
          console.error('Error in unified letter generation:', error);
          const errorEvent: StreamEvent = {
            type: 'error',
            content: error instanceof Error ? error.message : 'Unknown error occurred'
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorEvent)}\n\n`));
          controller.enqueue(encoder.encode('data: [DONE]\n\n'));
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'X-Accel-Buffering': 'no', // Disable Nginx buffering
      },
    });

  } catch (error) {
    console.error('Error in unified edge function:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to process request'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}

export const config: Config = {
  path: "/api/edge/generate-letter-unified"
};