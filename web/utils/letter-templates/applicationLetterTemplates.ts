/**
 * Application letter template collection
 * Contains predefined HTML templates for application letters
 */

export interface LetterTemplate {
  id: string;
  name: string;
  previewDescription: string;
  templateHtml: string;
  previewImagePath: string;
  isPremium: boolean;
  recommended: boolean;
  tokenCost: number;
  fontFamily: string;
}

export const plainTextTemplate: LetterTemplate = {
    id: "plain-text",
    name: "Plain Text",
    previewDescription: "Surat lamaran dengan format teks sederhana",
    previewImagePath: "/svgs/plain_text.svg",
    isPremium: false,
    tokenCost: 10,
    recommended: false,
    fontFamily: 'Roboto, sans-serif',
    templateHtml: `<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @media print {
            body { print-color-adjust: exact; }
            .no-print { display: none; }
        }
        
        .a4-page {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            background: white;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        @page {
            size: A4;
            margin: 0;
        }
        
        body {
            font-family: Roboto, sans-serif;
            line-height: 1.5;
            font-size: 15px;            
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
        }
        
        .date {
            text-align: right;
            margin-bottom: 30px;
        }
        
        .subject {
            margin-bottom: 20px;
        }
        
        .salutation {
            margin-bottom: 20px;
        }
        
        .body-text {
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .closing {
            margin-top: 40px;
        }
        
        .signature-space {
            margin: 60px 0 20px 0;
        }
    </style>
</head>
<body>
    <div class="a4-page">
        <div class="content-area">
            <div>
                <div class="header font-bold text-2xl tracking-wider">
                    <h1 >SURAT LAMARAN KERJA</h1>
                </div>

                <div class="date">
                    <p>26 Mei 2025</p>
                </div>

                <div class="subject">
                    <p>Perihal: Lamaran Pekerjaan sebagai Fullstack Developer</p>
                </div>

                <div class="salutation">
                    <p>
                        <span>Yth. Bapak/Ibu Bagian Sumber Daya Manusia,</span>
                    </p>
                </div>

                <div class="body-text">
                    <p>Dengan hormat,</p>
                </div>

                <div class="body-text">
                    <p>Berdasarkan informasi lowongan pekerjaan yang saya peroleh, saya ingin mengajukan diri untuk mengisi posisi Fullstack Developer di perusahaan yang Bapak/Ibu pimpin.</p>
                </div>

                <div class="body-text">
                    <p>Saya adalah seorang Software Engineer dengan pengalaman selama 6 tahun dalam pengembangan aplikasi Android, termasuk integrasi AI dan pengembangan lanjutan. Latar belakang pendidikan saya adalah Sarjana Teknik Informatika dari Universitas Ubudiyah Indonesia.</p>
                </div>

                <div class="body-text">
                    <p>Pengalaman saya meliputi pengembangan dan peluncuran aplikasi berbasis AI dengan lebih dari 100 ribu unduhan dan rating 4.8 di Play Store. Saya memiliki keahlian dalam integrasi model AI, pengembangan tampilan kustom dengan canvas, implementasi fitur auto-connect dengan Bluetooth, dan migrasi aplikasi Android native ke Flutter. Selain itu, saya juga berpengalaman dalam pengembangan backend, scripting Python, integrasi CI/CD, dan pemecahan masalah.</p>
                </div>

                <div class="body-text">
                    <p>Saya yakin bahwa kemampuan problem-solving dan komunikasi yang saya miliki, serta pengalaman saya dalam pengembangan aplikasi dan integrasi AI, akan memberikan kontribusi positif bagi perusahaan Bapak/Ibu. Saya memiliki antusiasme yang tinggi untuk terus mengembangkan diri dan mempelajari teknologi baru yang relevan dengan kebutuhan perusahaan.</p>
                </div>

                <div class="body-text">
                    <p>Untuk informasi lebih rinci mengenai kualifikasi dan pengalaman kerja saya, terlampir bersama surat ini adalah Curriculum Vitae (CV) saya.</p>
                </div>

                <div class="body-text">
                    <p>Besar harapan saya untuk dapat diberikan kesempatan wawancara agar dapat menjelaskan lebih lanjut mengenai potensi diri saya dan bagaimana saya dapat berkontribusi bagi kesuksesan perusahaan Bapak/Ibu.</p>
                </div>

                <div class="body-text">
                    <p>Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih.</p>
                </div>
            </div>

            <div>
                <div class="closing">
                    <p>Hormat saya,</p>
                    <div class="signature-space"></div>
                    <p>Amwalul Ikhsani</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`
}

/**
 * Classic Blue Template
 * A formal business letter with blue accents
 */
export const classicBlueTemplate: LetterTemplate = {
  id: "classic-blue",
  name: "Classic Blue",
  previewDescription: "Surat formal dengan aksen biru dan layout profesional",
  previewImagePath: "/svgs/classic_blue.svg",
  isPremium: true,
  tokenCost: 15,
  recommended: false,
  fontFamily: "'Merriweather', serif",
  templateHtml: `<!DOCTYPE html>
<html lang="id">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Surat Lamaran Kerja</title>
   <script src="https://cdn.tailwindcss.com"></script>
   <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@400;500;600;700&display=swap" rel="stylesheet">
   <style>
       @media print {
           body { print-color-adjust: exact; }
           .no-print { display: none; }
       }
       
       .a4-page {
           width: 210mm;
           height: 297mm;
           margin: 0 auto;
           background: white;
           overflow: hidden;
           display: flex;
           flex-direction: column;
       }
       
       @page {
           size: A4;
           margin: 0;
       }
       
       body {
           font-family: 'Merriweather', serif;
           line-height: 1.5;
           font-size: 15px;
       }
       
       .content-area {
           flex: 1;
           display: flex;
           flex-direction: column;
           justify-content: space-between;
       }
   </style>
</head>
<body>
   <div class="a4-page p-8"> 
       <div class="content-area">
           <div>
               <div class="border-b-2 border-blue-600 pb-4 mb-8">
                   <div class="flex items-center justify-between">
                       <div class="w-16 h-1 bg-blue-600"></div>
                       <h1 class="text-2xl font-bold text-blue-900 tracking-wider">SURAT LAMARAN KERJA</h1>
                       <div class="w-16 h-1 bg-blue-600"></div>
                   </div>
               </div>

               <div class="text-right mb-8">
                   <p class="text-gray-700 font-medium">26 Mei 2025</p>
               </div>

               <div class="mb-6">
                   <p class="font-semibold text-gray-800">
                       <span>Perihal: Lamaran Pekerjaan sebagai Fullstack Developer</span>
                   </p>
               </div>

               <div class="mb-8">
                   <p class="text-gray-800">
                       <span class="font-semibold">Yth. Bapak/Ibu Bagian Sumber Daya Manusia,</span>
                   </p>
               </div>

               <div class="mb-6">
                   <p>Dengan hormat,</p>
               </div>

               <div class="space-y-5 text-justify text-gray-800 leading-relaxed">
                   <p>
                       Berdasarkan informasi lowongan pekerjaan yang saya peroleh, saya ingin mengajukan diri untuk mengisi posisi Fullstack Developer di perusahaan yang Bapak/Ibu pimpin.
                   </p>

                   <p>
                       Saya adalah seorang Software Engineer dengan pengalaman selama 6 tahun dalam pengembangan aplikasi Android, termasuk integrasi AI dan pengembangan lanjutan. Latar belakang pendidikan saya adalah Sarjana Teknik Informatika dari Universitas Ubudiyah Indonesia.
                   </p>

                   <p>
                       Pengalaman saya meliputi pengembangan dan peluncuran aplikasi berbasis AI dengan lebih dari 100 ribu unduhan dan rating 4.8 di Play Store. Saya memiliki keahlian dalam integrasi model AI, pengembangan tampilan kustom dengan canvas, implementasi fitur auto-connect dengan Bluetooth, dan migrasi aplikasi Android native ke Flutter. Selain itu, saya juga berpengalaman dalam pengembangan backend, scripting Python, integrasi CI/CD, dan pemecahan masalah.
                   </p>

                   <p>
                       Saya yakin bahwa kemampuan problem-solving dan komunikasi yang saya miliki, serta pengalaman saya dalam pengembangan aplikasi dan integrasi AI, akan memberikan kontribusi positif bagi perusahaan Bapak/Ibu. Saya memiliki antusiasme yang tinggi untuk terus mengembangkan diri dan mempelajari teknologi baru yang relevan dengan kebutuhan perusahaan.
                   </p>

                   <p>
                       Untuk informasi lebih rinci mengenai kualifikasi dan pengalaman kerja saya, terlampir bersama surat ini adalah Curriculum Vitae (CV) saya.
                   </p>

                   <p>
                       Besar harapan saya untuk dapat diberikan kesempatan wawancara agar dapat menjelaskan lebih lanjut mengenai potensi diri saya dan bagaimana saya dapat berkontribusi bagi kesuksesan perusahaan Bapak/Ibu.
                   </p>

                   <p>
                       Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih.
                   </p>
               </div>
           </div>

           <div>
               <div class="mt-10 mb-8">
                   <p class="text-gray-800 font-medium">Hormat saya,</p>
                   
                   <div class="mt-16 mb-4">
                       <div class="w-48 border-b border-gray-400"></div>
                   </div>
                   
                   <p class="font-bold text-gray-900">Amwalul Ikhsani</p>
               </div>

               <div class="pt-4 border-t border-gray-300">
                   <div class="flex justify-center">
                       <div class="flex space-x-2">
                           <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
                           <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                           <div class="w-2 h-2 bg-blue-300 rounded-full"></div>
                       </div>
                   </div>
               </div>
           </div>
       </div>
   </div>
</body>
</html>`
};

/**
 * Modern Professional Template
 * A premium business letter with elegant design and PDF optimization
 */
export const professionalClassicTemplate: LetterTemplate = {
  id: "professional-classic",
  name: "Professional Classic",
  previewDescription: "Desain profesional dengan header biru dan judul tercentrasi",
  previewImagePath: "/svgs/professional_classic.svg",
  isPremium: true,
  tokenCost: 15,
  recommended: true,
  fontFamily: 'Inter, sans-serif',
  templateHtml: `<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Inter', sans-serif; 
            line-height: 1;
            font-size: 14px; 
        }
        .a4-page { 
            width: 210mm; 
            height: 297mm; 
            display: flex; 
            flex-direction: column; 
        }
        .content-body { 
            flex-grow: 1; 
        }
    </style>
</head>
<body>
    <div class="a4-page max-w-4xl mx-auto bg-white">
        <!-- Header -->
        <div class="bg-blue-800 text-white py-6 px-8">
            <h1 class="text-2xl font-bold text-center tracking-wide">SURAT LAMARAN KERJA</h1>
        </div>
        
        <!-- Date and Header Info -->
        <div class="px-8 pt-8">
            <div class="text-right mb-4">
                <p class="font-medium text-gray-800">27 Mei 2025</p>
            </div>
            
            <div class="mb-4">
                <p class="font-semibold text-gray-800 mb-4">Perihal: Lamaran Pekerjaan sebagai CSR GraPARI</p>
                <p class="leading-relaxed text-gray-700">
                    Yth. Bapak/Ibu Bagian Sumber Daya Manusia<br>
                    Infomedia<br>
                    Kota Kediri, Jawa Timur
                </p>
            </div>
        </div>
        
        <!-- Content Body -->
        <div class="content-body px-8">
            <div class="space-y-4 leading-loose text-justify text-gray-700">
                <p>Dengan hormat,</p>
                
                <p>Berdasarkan informasi lowongan pekerjaan yang saya peroleh, saya ingin mengajukan diri untuk mengisi posisi sebagai CSR GraPARI di Infomedia, Kota Kediri. Saya sangat tertarik dengan kesempatan ini karena saya yakin bahwa latar belakang pendidikan dan pengalaman saya relevan dengan kebutuhan pekerjaan ini.</p>
                
                <p>Saya adalah lulusan Sarjana Teknik Informatika dari Universitas Ubudiyah Indonesia. Selama masa studi, saya telah mengembangkan kemampuan problem solving dan beradaptasi dengan cepat. Pengalaman saya sebagai Software Engineer di Perisian Huda Sdn Bhd, MODA | PT Mobilitas Digital Indonesia, dan sebagai freelancer telah melatih saya dalam memahami kebutuhan pelanggan dan memberikan solusi yang efektif. Saya terbiasa bekerja dalam tim dan berkomunikasi dengan berbagai pihak untuk mencapai tujuan bersama.</p>
                
                <p>Meskipun pengalaman saya lebih berfokus pada pengembangan aplikasi, saya yakin kemampuan saya dalam mengidentifikasi masalah, memberikan solusi, dan berkomunikasi dengan baik dapat saya terapkan dalam peran sebagai CSR GraPARI. Saya memiliki kemampuan untuk belajar dengan cepat dan beradaptasi dengan lingkungan kerja yang baru. Saya juga memiliki ketertarikan yang besar untuk terus mengembangkan diri dan meningkatkan kemampuan saya.</p>
                
                <p>Saya sangat antusias untuk berkontribusi pada Infomedia dan memberikan pelayanan terbaik kepada pelanggan Telkomsel. Saya percaya bahwa dengan kemampuan dan semangat yang saya miliki, saya dapat memberikan nilai tambah bagi perusahaan.</p>
                
                <p>Sebagai bahan pertimbangan lebih lanjut, saya lampirkan Curriculum Vitae (CV) yang berisi informasi lebih detail mengenai latar belakang pendidikan, pengalaman kerja, dan keterampilan yang saya miliki.</p>
                
                <p>Besar harapan saya untuk dapat diberikan kesempatan wawancara agar saya dapat menjelaskan lebih lanjut mengenai potensi diri saya. Atas perhatian dan waktu Bapak/Ibu, saya ucapkan terima kasih.</p>
            </div>
        </div>
        
        <!-- Signature Section - Fixed at bottom -->
        <div class="px-8 pb-4 mt-auto">
            <div class="pt-6">
                <p class="font-semibold text-gray-800 mb-16">Hormat saya,</p>
                <div>
                    <p class="font-bold text-gray-800 border-b-2 border-blue-800 inline-block pb-1">Amwalul Ikhsani</p>
                </div>
            </div>
        </div>
        
        <!-- Footer with spacing -->
        <div class="bg-blue-800 h-4 mt-4"></div>
    </div>
</body>
</html>`
};

/**
 * Minimalist Sidebar Template
 * A clean design with left sidebar accent, minimal typography, and subtle geometric elements
 */
export const minimalistSidebarTemplate: LetterTemplate = {
  id: "minimalist-sidebar",
  name: "Minimalist Sidebar",
  previewDescription: "Desain modern dengan sidebar indigo di sisi kiri, tata letak premium, dan spasi bersih",
  previewImagePath: "/svgs/minimalist_sidebar.svg",
  isPremium: true,
  tokenCost: 15,
  recommended: false,
  fontFamily: 'Source Sans Pro, sans-serif',
  templateHtml: `<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Source Sans Pro', sans-serif; }
        .a4-page { height: 297mm; width: 210mm; margin: 0 auto; overflow: hidden; }
        .letter-content { height: 100%; display: flex; font-size: 15px; line-height: 1.6; }
        .main-content { flex-grow: 1; display: flex; flex-direction: column; }
        .content-body { flex-grow: 1; }
        @media print { .a4-page { margin: 0; } }
    </style>
</head>
<body class="bg-white m-0 p-0">
    <div class="a4-page bg-white">
        <div class="letter-content">
            <!-- Left Sidebar -->
            <div class="w-4 bg-indigo-600"></div>
            
            <!-- Main Content -->
            <div class="main-content">
                <!-- Header -->
                <div class="bg-gray-50 border-b border-gray-200 px-8 py-5">
                    <h1 class="text-lg font-semibold text-gray-800 tracking-wide">SURAT LAMARAN KERJA</h1>
                </div>
                
                <!-- Date -->
                <div class="px-8 pt-6 pb-2">
                    <div class="text-right">
                        <span class="bg-indigo-50 text-indigo-800 px-3 py-1 rounded text-sm font-medium">27 Mei 2025</span>
                    </div>
                </div>
                
                <!-- Header Info -->
                <div class="px-8 pb-4">
                    <div class="border-l-2 border-indigo-200 pl-4 mb-4">
                        <p class="font-medium text-gray-800 mb-1">Perihal: Lamaran Pekerjaan sebagai CSR GraPARI</p>
                    </div>
                    
                    <div class="text-gray-700 text-sm">
                        <p>Yth. Bapak/Ibu Bagian Sumber Daya Manusia</p>
                        <p class="font-medium">Infomedia</p>
                        <p>Kota Kediri, Jawa Timur</p>
                    </div>
                </div>
                
                <!-- Content Body -->
                <div class="content-body px-8">
                    <div class="space-y-4 text-justify text-gray-700">
                        <p>Dengan hormat,</p>
                        
                        <p>Berdasarkan informasi lowongan pekerjaan yang saya peroleh, saya ingin mengajukan diri untuk mengisi posisi sebagai CSR GraPARI di Infomedia, Kota Kediri. Saya sangat tertarik dengan kesempatan ini karena saya yakin bahwa latar belakang pendidikan dan pengalaman saya relevan dengan kebutuhan pekerjaan ini.</p>
                        
                        <p>Saya adalah lulusan Sarjana Teknik Informatika dari Universitas Ubudiyah Indonesia. Selama masa studi, saya telah mengembangkan kemampuan problem solving dan beradaptasi dengan cepat. Pengalaman saya sebagai Software Engineer di Perisian Huda Sdn Bhd, MODA | PT Mobilitas Digital Indonesia, dan sebagai freelancer telah melatih saya dalam memahami kebutuhan pelanggan dan memberikan solusi yang efektif. Saya terbiasa bekerja dalam tim dan berkomunikasi dengan berbagai pihak untuk mencapai tujuan bersama.</p>
                        
                        <p>Meskipun pengalaman saya lebih berfokus pada pengembangan aplikasi, saya yakin kemampuan saya dalam mengidentifikasi masalah, memberikan solusi, dan berkomunikasi dengan baik dapat saya terapkan dalam peran sebagai CSR GraPARI. Saya memiliki kemampuan untuk belajar dengan cepat dan beradaptasi dengan lingkungan kerja yang baru. Saya juga memiliki ketertarikan yang besar untuk terus mengembangkan diri dan meningkatkan kemampuan saya.</p>
                        
                        <p>Saya sangat antusias untuk berkontribusi pada Infomedia dan memberikan pelayanan terbaik kepada pelanggan Telkomsel. Saya percaya bahwa dengan kemampuan dan semangat yang saya miliki, saya dapat memberikan nilai tambah bagi perusahaan.</p>
                        
                        <p>Sebagai bahan pertimbangan lebih lanjut, saya lampirkan Curriculum Vitae (CV) yang berisi informasi lebih detail mengenai latar belakang pendidikan, pengalaman kerja, dan keterampilan yang saya miliki.</p>
                        
                        <p>Besar harapan saya untuk dapat diberikan kesempatan wawancara agar saya dapat menjelaskan lebih lanjut mengenai potensi diri saya. Atas perhatian dan waktu Bapak/Ibu, saya ucapkan terima kasih.</p>
                    </div>
                </div>
                
                <!-- Signature Section - Fixed at bottom -->
                <div class="px-8 pb-8 mt-auto">
                    <div class="pt-6">
                        <p class="font-medium text-gray-800 mb-16">Hormat saya,</p>
                        <div>
                            <p class="font-medium text-gray-800">Amwalul Ikhsani</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`
};

export const minimalistBorderFrameTemplate: LetterTemplate = {
    id: "minimalist-border-frame",
    name: "Minimalist Border Frame",
    previewDescription: "Desain elegan dengan bingkai border, detail sudut halus berwarna hijau, dan hierarki baris yang rapi",
    previewImagePath: "/svgs/minimalist_border.svg",
    isPremium: true,
    tokenCost: 15,
    recommended: false,
    fontFamily: 'Lato, sans-serif',
    templateHtml: `<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Lato', sans-serif; 
        }
        .a4-page { 
            height: 297mm; 
            width: 210mm; 
            margin: 0 auto; 
            overflow: hidden; 
        }
        .letter-content { 
            height: 100%; 
            display: flex; 
            flex-direction: column; 
            font-size: 15px; 
            line-height: 1.6; 
            position: relative; 
        }
        .content-body { 
            flex-grow: 1; 
        }
        .border-l-3 { 
            border-left-width: 3px; 
        }
        .signature-section { 
            position: absolute; 
            bottom: 48px; 
            left: 32px; 
            right: 32px; 
        }
        .bottom-decoration { 
            position: absolute; 
            bottom: 24px; 
            right: 32px; 
        }
        @media print { 
            .a4-page { 
                margin: 0; 
            } 
        }
    </style>
</head>
<body class="bg-white m-0 p-0">
    <div class="a4-page bg-white">
        <div class="letter-content">
            <!-- Header -->
            <div class="border-b border-gray-200 px-8 py-6">
                <div class="relative">
                    <div class="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-green-500"></div>
                    <div class="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-green-500"></div>
                    <h1 class="text-lg font-medium text-center text-gray-800 py-2">SURAT LAMARAN KERJA</h1>
                </div>
            </div>

            <!-- Date and Subject Section -->
            <div class="px-8 pt-6 pb-4">
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="col-span-2">
                        <div class="border-l-3 border-green-400 pl-3">
                            <p class="font-medium text-gray-800 text-sm">Perihal:</p>
                            <p class="text-gray-700 text-sm">Lamaran Pekerjaan sebagai CSR GraPARI</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="inline-block border border-green-300 px-3 py-1 rounded">
                            <p class="text-green-700 font-medium text-sm">6 Mei 2025</p>
                        </div>
                    </div>
                </div>

                <div class="text-gray-700 text-sm">
                    <p>Yth. Bapak/Ibu Bagian Sumber Daya Manusia</p>
                    <p class="font-medium">Infomedia</p>
                    <p>Kota Kediri, Jawa Timur</p>
                </div>
            </div>

            <!-- Content Body -->
            <div class="content-body px-8 pb-32">
                <div class="space-y-4 text-justify text-gray-700">
                    <p>Dengan hormat,</p>

                    <p>Berdasarkan informasi lowongan pekerjaan yang saya peroleh, saya ingin mengajukan diri untuk posisi CSR GraPARI di Infomedia Kediri. Saya memiliki latar belakang pendidikan Sarjana Teknik Informatika dari Universitas Ubudiyah Indonesia.</p>

                    <p>Pengalaman saya dalam pengembangan aplikasi Android, seperti yang tertera pada CV terlampir, telah membekali saya dengan kemampuan problem solving dan pemahaman kebutuhan pelanggan. Saya terbiasa berinteraksi dengan tim untuk memahami spesifikasi dan memberikan solusi yang efektif. Kemampuan adaptasi dan keinginan untuk terus belajar akan membantu saya dalam memberikan pelayanan informasi dan menangani keluhan pelanggan Telkomsel.</p>

                    <p>Saya sangat antusias dengan kesempatan untuk berkontribusi pada Infomedia dan memberikan pelayanan terbaik bagi pelanggan Telkomsel. Saya yakin kemampuan komunikasi dan analisis saya akan menjadi aset berharga bagi tim CSR GraPARI.</p>

                    <p>Besar harapan saya untuk dapat diberikan kesempatan wawancara agar dapat menjelaskan lebih detail mengenai kualifikasi dan motivasi saya. Atas perhatian dan waktu Bapak/Ibu, saya ucapkan terima kasih.</p>
                </div>
            </div>

            <!-- Signature Section - Fixed at Bottom -->
            <div class="signature-section">
                <div class="mt-8 pt-4">
                    <div class="mb-12">
                        <p class="font-medium text-gray-800">Hormat saya,</p>
                    </div>
                    <div class="relative">
                        <p class="font-medium text-gray-800">Amwalul Ikhsani</p>
                        <div class="w-32 h-px bg-green-400 mt-1"></div>
                    </div>
                </div>
            </div>

            <!-- Bottom Decoration - Separate from signature -->
            <div class="bottom-decoration">
                <div class="w-4 h-4 border-b-2 border-r-2 border-green-400"></div>
            </div>
        </div>
    </div>
</body>
</html>`
};

export const minimalistAccentTemplate: LetterTemplate = {
    id: "minimalist-accent",
    name: "Minimalist Accents",
    previewDescription: "Desain modern dan bersih dengan aksen kuning, spasi teratur, dan elemen dekoratif minimalis",
    previewImagePath: "/svgs/minimalist_accent.svg",
    isPremium: true,
    tokenCost: 15,
    recommended: false,
    fontFamily: 'IBM Plex Sans, sans-serif',
    templateHtml: `<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'IBM Plex Sans', sans-serif; }
        .a4-page { height: 297mm; width: 210mm; margin: 0 auto; overflow: hidden; }
        .letter-content { height: 100%; display: flex; flex-direction: column; font-size: 14px; line-height: 1.6; position: relative; }
        .content-body { flex-grow: 1; }
        .letter-spacing-wide { letter-spacing: 0.05em; }
        .space-y-5 > * + * { margin-top: 1.25rem; }
        .signature-section { position: absolute; bottom: 60px; left: 40px; right: 40px; }
        .bottom-decoration { position: absolute; bottom: 32px; right: 40px; }
        @media print { .a4-page { margin: 0; } }
    </style>
</head>
<body class="bg-white m-0 p-0">
    <div class="a4-page bg-white">
        <div class="letter-content">
            <!-- Minimal Header -->
            <div class="px-10 pt-8 pb-6">
                <div class="text-center mb-6">
                    <h1 class="text-xl font-medium text-gray-800 letter-spacing-wide">SURAT LAMARAN KERJA</h1>
                    <div class="flex justify-center items-center mt-3">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                        <div class="w-8 h-px bg-yellow-300 mx-1"></div>
                        <div class="w-1 h-1 bg-yellow-200 rounded-full"></div>
                    </div>
                </div>
                
                <div class="flex justify-between items-center mb-6">
                    <div class="text-gray-700">
                        <p class="font-medium mb-1">Perihal:</p>
                        <p class="text-sm">Lamaran Pekerjaan sebagai CSR GraPARI</p>
                    </div>
                    <div class="text-right text-yellow-600">
                        <p class="font-medium">27</p>
                        <p class="text-xs">MEI 2025</p>
                    </div>
                </div>
                
                <div class="text-gray-700">
                    <p class="mb-1">Yth. Bapak/Ibu Bagian Sumber Daya Manusia</p>
                    <p class="font-medium mb-1">Infomedia</p>
                    <p>Kota Kediri, Jawa Timur</p>
                </div>
            </div>
            
            <!-- Content Body -->
            <div class="content-body px-10 pb-40">
                <div class="space-y-5 text-justify text-gray-700">
                    <div class="mb-5">
                        <div class="w-3 h-px bg-yellow-400 mb-3"></div>
                        <p>Dengan hormat,</p>
                    </div>
                    
                    <p>Berdasarkan informasi lowongan pekerjaan yang saya peroleh, saya ingin mengajukan diri untuk mengisi posisi sebagai CSR GraPARI di Infomedia, Kota Kediri. Saya sangat tertarik dengan kesempatan ini karena saya yakin bahwa latar belakang pendidikan dan pengalaman saya relevan dengan kebutuhan pekerjaan ini.</p>
                    
                    <p>Saya adalah lulusan Sarjana Teknik Informatika dari Universitas Ubudiyah Indonesia. Selama masa studi, saya telah mengembangkan kemampuan problem solving dan beradaptasi dengan cepat. Pengalaman saya sebagai Software Engineer di Perisian Huda Sdn Bhd, MODA | PT Mobilitas Digital Indonesia, dan sebagai freelancer telah melatih saya dalam memahami kebutuhan pelanggan dan memberikan solusi yang efektif. Saya terbiasa bekerja dalam tim dan berkomunikasi dengan berbagai pihak untuk mencapai tujuan bersama.</p>
                    
                    <p>Meskipun pengalaman saya lebih berfokus pada pengembangan aplikasi, saya yakin kemampuan saya dalam mengidentifikasi masalah, memberikan solusi, dan berkomunikasi dengan baik dapat saya terapkan dalam peran sebagai CSR GraPARI. Saya memiliki kemampuan untuk belajar dengan cepat dan beradaptasi dengan lingkungan kerja yang baru. Saya juga memiliki ketertarikan yang besar untuk terus mengembangkan diri dan meningkatkan kemampuan saya.</p>
                    
                    <p>Sebagai bahan pertimbangan lebih lanjut, saya lampirkan Curriculum Vitae (CV) yang berisi informasi lebih detail mengenai latar belakang pendidikan, pengalaman kerja, dan keterampilan yang saya miliki.</p>
                    
                    <p>Besar harapan saya untuk dapat diberikan kesempatan wawancara agar saya dapat menjelaskan lebih lanjut mengenai potensi diri saya. Atas perhatian dan waktu Bapak/Ibu, saya ucapkan terima kasih.</p>
                </div>
            </div>
            
            <!-- Signature Section - Fixed at Bottom -->
            <div class="signature-section">
                <p class="font-medium text-gray-800 mb-16">Hormat saya,</p>
                <div class="flex items-end justify-between">
                    <div>
                        <p class="font-medium text-gray-800">Amwalul Ikhsani</p>
                        <div class="w-24 h-px bg-yellow-400 mt-2"></div>
                    </div>
                </div>
            </div>

            <!-- Bottom Decoration - Separate from signature -->
            <div class="bottom-decoration">
                <div class="flex items-center">
                    <div class="w-1 h-1 bg-yellow-200 rounded-full"></div>
                    <div class="w-2 h-px bg-yellow-300 mx-1"></div>
                    <div class="w-1 h-1 bg-yellow-400 rounded-full"></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`
};

export const minimalistCircularAccentsTemplate: LetterTemplate = {
    id: "minimalist-circular",
    name: "Minimalist Circular Accents",
    previewDescription: "Desain modern dengan elemen aksen lingkaran ungu, spasi yang bersih, dan pola titik yang halus",
    previewImagePath: "/svgs/minimalist_circular_accent.svg",
    isPremium: true,
    tokenCost: 15,
    recommended: false,
    fontFamily: 'Open Sans, sans-serif',
    templateHtml: `<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Open Sans', sans-serif; }
        .a4-page { height: 297mm; width: 210mm; margin: 0 auto; overflow: hidden; }
        .letter-content { height: 100%; display: flex; flex-direction: column; font-size: 14px; line-height: 1.6; }
        .content-body { flex-grow: 1; }
        @media print { .a4-page { margin: 0; } }
    </style>
</head>
<body class="bg-white m-0 p-0">
    <div class="a4-page bg-white">
        <div class="letter-content">
            <!-- Header with Circles -->
            <div class="px-8 pt-8 pb-6 relative">
                <div class="absolute top-8 left-4 w-3 h-3 bg-purple-300 rounded-full opacity-60"></div>
                <div class="absolute top-12 right-8 w-2 h-2 bg-purple-200 rounded-full opacity-40"></div>
                <div class="absolute top-8 right-16 w-1 h-1 bg-purple-400 rounded-full"></div>
                
                <div class="text-center mb-6">
                    <h1 class="text-lg font-medium text-gray-800 mb-3">SURAT LAMARAN KERJA</h1>
                    <div class="flex justify-center items-center space-x-2">
                        <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                        <div class="w-12 h-px bg-purple-300"></div>
                        <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                    </div>
                </div>
            </div>
            
            <!-- Date and Subject -->
            <div class="px-8 pb-6">
                <div class="flex items-start justify-between mb-6">
                    <div class="flex items-start space-x-3">
                        <div>
                            <p class="font-medium text-gray-800 text-sm mb-1">Perihal:</p>
                            <p class="text-gray-700 text-sm">Lamaran Pekerjaan sebagai CSR GraPARI</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <p class="text-purple-700 font-medium text-sm">27 Mei 2025</p>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="absolute top-2 right-2 w-2 h-2 bg-purple-200 rounded-full opacity-60"></div>
                    <div class="text-gray-700 text-sm">
                        <p>Yth. Bapak/Ibu Bagian Sumber Daya Manusia</p>
                        <p class="font-medium">Infomedia</p>
                        <p>Kota Kediri, Jawa Timur</p>
                    </div>
                </div>
            </div>
            
            <!-- Content Body -->
            <div class="content-body px-8">
                <div class="space-y-4 text-justify text-gray-700">
                    
                    <p>Dengan hormat,</p>
                    
                    <p>Berdasarkan informasi lowongan pekerjaan yang saya peroleh, saya ingin mengajukan diri untuk mengisi posisi sebagai CSR GraPARI di Infomedia, Kota Kediri. Saya sangat tertarik dengan kesempatan ini karena saya yakin bahwa latar belakang pendidikan dan pengalaman saya relevan dengan kebutuhan pekerjaan ini.</p>
                    
                    <p>Saya adalah lulusan Sarjana Teknik Informatika dari Universitas Ubudiyah Indonesia. Selama masa studi, saya telah mengembangkan kemampuan problem solving dan beradaptasi dengan cepat. Pengalaman saya sebagai Software Engineer di Perisian Huda Sdn Bhd, MODA | PT Mobilitas Digital Indonesia, dan sebagai freelancer telah melatih saya dalam memahami kebutuhan pelanggan dan memberikan solusi yang efektif. Saya terbiasa bekerja dalam tim dan berkomunikasi dengan berbagai pihak untuk mencapai tujuan bersama.</p>
                    
                    <p>Meskipun pengalaman saya lebih berfokus pada pengembangan aplikasi, saya yakin kemampuan saya dalam mengidentifikasi masalah, memberikan solusi, dan berkomunikasi dengan baik dapat saya terapkan dalam peran sebagai CSR GraPARI. Saya memiliki kemampuan untuk belajar dengan cepat dan beradaptasi dengan lingkungan kerja yang baru. Saya juga memiliki ketertarikan yang besar untuk terus mengembangkan diri dan meningkatkan kemampuan saya.</p>
                                        
                    <p>Saya sangat antusias untuk berkontribusi pada Infomedia dan memberikan pelayanan terbaik kepada pelanggan Telkomsel. Saya percaya bahwa dengan kemampuan dan semangat yang saya miliki, saya dapat memberikan nilai tambah bagi perusahaan.</p>
                    
                    <p>Sebagai bahan pertimbangan lebih lanjut, saya lampirkan Curriculum Vitae (CV) yang berisi informasi lebih detail mengenai latar belakang pendidikan, pengalaman kerja, dan keterampilan yang saya miliki.</p>
                    
                    <p>Besar harapan saya untuk dapat diberikan kesempatan wawancara agar saya dapat menjelaskan lebih lanjut mengenai potensi diri saya. Atas perhatian dan waktu Bapak/Ibu, saya ucapkan terima kasih.</p>
                </div>
            </div>
            
            <!-- Signature Section - Fixed at bottom -->
            <div class="px-8 pb-8 mt-auto">
                <div class="pt-6">
                    <p class="font-medium text-gray-800 mb-16">Hormat saya,</p>
                    <div>
                        <p class="font-medium text-gray-800 mb-2">Amwalul Ikhsani</p>
                        <div class="flex items-center space-x-1">
                            <div class="w-32 h-px bg-purple-400"></div>
                            <div class="w-1 h-1 bg-purple-400 rounded-full"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Bottom Decorative Elements with spacing -->
                <div class="flex justify-end mt-8 pt-4">
                    <div class="flex space-x-1">
                        <div class="w-1 h-1 bg-purple-200 rounded-full"></div>
                        <div class="w-2 h-2 bg-purple-300 rounded-full opacity-60"></div>
                        <div class="w-1 h-1 bg-purple-400 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`
};

// Collection of all available templates
export const applicationLetterTemplates: LetterTemplate[] = [
  plainTextTemplate,
  classicBlueTemplate,
  professionalClassicTemplate,
  minimalistSidebarTemplate,
  minimalistBorderFrameTemplate,
  minimalistAccentTemplate,
  minimalistCircularAccentsTemplate,
];

// Get template by ID
export function getTemplateById(id: string): LetterTemplate | undefined {
  return applicationLetterTemplates.find(template => template.id === id);
}