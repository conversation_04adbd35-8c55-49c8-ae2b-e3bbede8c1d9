/**
 * Utilitas untuk pengelolaan resume
 * Fokus pada operasi yang berhubungan dengan validasi dan manajemen resume
 */

import { authGet, authPostFormData, authDelete } from '@/lib/authFetch';
import rollbar from '@/lib/rollbar';

// Tipe data untuk resume
export interface ResumeInfo {
  fileName: string;
  publicUrl: string;
  uploadedAt: string;
  unauthenticatedResumeFile?: File;
}

// Fungsi untuk validasi file resume
export function validateResumeFile(file: File): { valid: boolean; error?: string } {
  // Validasi tipe file
  const allowedTypes = [
    'application/pdf', 
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/png',
    'image/jpeg',
    'image/jpg'
  ];
  
  if (!allowedTypes.includes(file.type)) {
    return { 
      valid: false, 
      error: 'Format file tidak didukung. Harap unggah file PDF, DOCX, PNG, JPG, atau JPEG' 
    };
  }

  // Validasi ukuran file (maksimal 5MB)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return { 
      valid: false, 
      error: 'Ukuran file terlalu besar. Maksimal 5MB.' 
    };
  }

  return { valid: true };
}

// Fungsi untuk upload resume
export async function uploadResume(file: File): Promise<{ 
  success: boolean; 
  data?: any; 
  error?: string 
}> {
  try {
    const formData = new FormData();
    formData.append('resume', file);

    const response = await authPostFormData('/api/upload-resume', formData);

    const data = await response.json();
    
    if (response.ok && data.success) {
      return { 
        success: true, 
        data: data.data 
      };
    } else {
      return { 
        success: false, 
        error: data.error || 'Terjadi kesalahan saat mengunggah resume' 
      };
    }
  } catch (err) {
    console.error('Error uploading resume:', err);
    return { 
      success: false, 
      error: 'Gagal mengunggah resume. Silakan coba lagi.' 
    };
  }
}

// Fungsi untuk mendapatkan resume yang ada
export async function fetchExistingResume(): Promise<{
  success: boolean;
  hasResume: boolean;
  resumeInfo?: ResumeInfo;
  error?: string;
}> {
  try {
    const response = await authGet('/api/upload-resume');
    const data = await response.json();
    
    if (response.ok) {
      if (data.hasResume) {
        return {
          success: true,
          hasResume: true,
          resumeInfo: {
            fileName: data.fileName,
            publicUrl: data.publicUrl,
            uploadedAt: data.uploadedAt,
          }
        };
      } else {
        return {
          success: true,
          hasResume: false
        };
      }
    } else {
      return {
        success: false,
        hasResume: false,
        error: data.error || 'Gagal mendapatkan resume'
      };
    }
  } catch (err) {
    console.error('Error fetching resume:', err);
    return {
      success: false,
      hasResume: false,
      error: 'Gagal memeriksa resume. Silakan coba lagi.'
    };
  }
}

// Fungsi untuk menghapus resume
export async function deleteResume(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const response = await authDelete('/api/upload-resume', {});
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      return { success: true };
    } else {
      return { 
        success: false, 
        error: data.error || 'Terjadi kesalahan saat menghapus resume' 
      };
    }
  } catch (err) {
    console.error('Error deleting resume:', err);
    return { 
      success: false, 
      error: 'Gagal menghapus resume. Silakan coba lagi.' 
    };
  }
}

// Fungsi untuk mendapatkan URL resume
export async function getResumeUrl(download: boolean = false): Promise<{
  success: boolean;
  url?: string;
  error?: string;
}> {
  try {
    const response = await authGet(`/api/get-resume-url?download=${download}`);
    const data = await response.json();
    
    if (response.ok && data.success) {
      return { 
        success: true, 
        url: data.url 
      };
    } else {
      return { 
        success: false, 
        error: data.error || 'Tidak dapat mengakses resume' 
      };
    }
  } catch (err) {
    console.error('Error getting resume URL:', err);
    return { 
      success: false, 
      error: 'Gagal mengakses resume. Silakan coba lagi.' 
    };
  }
}
