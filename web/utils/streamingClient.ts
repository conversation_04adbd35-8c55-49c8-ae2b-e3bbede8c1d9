/**
 * Handles Server-Sent Events (SSE) streaming from Edge Functions
 */

import { authFetch } from '@/lib/authFetch';
import { fileToBase64, getMimeTypeFromFileName } from '@/utils/fileUtils';
import { extractTextFromDocx } from '@/utils/docxUtils';

export interface StreamingOptions {
  onChunk?: (text: string) => void;
  onComplete?: (html: string, letterId?: string) => void;
  onError?: (error: string) => void;
}

export interface UnifiedStreamingOptions {
  onStatus?: (message: string, phase: 'plain-text' | 'template') => void;
  onPlainTextChunk?: (text: string) => void;
  onTemplateChunk?: (html: string) => void;
  onComplete?: (data: {
    plainText: string;
    templateHtml: string | null;
    templateId: string;
    letterId?: string;
  }) => void;
  onError?: (error: string) => void;
}

export interface UnifiedStreamingData {
  resumeData: string; // Base64 encoded or extracted text
  resumeMimeType: string;
  jobDescription?: string;
  jobImage?: string; // Base64 encoded
  templateId: string;
  templateHtml?: string;
  userId?: string;
  letterId?: string;
  isEdit?: boolean;
}

/**
 * Streams unified letter generation (both plain text and template)
 */
export async function streamUnifiedLetterGeneration(
  data: UnifiedStreamingData,
  options: UnifiedStreamingOptions
): Promise<void> {
  const { onStatus, onPlainTextChunk, onTemplateChunk, onComplete, onError } = options;

  try {
    const response = await authFetch('/api/edge/generate-letter-unified', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error('No response body');
    }

    let buffer = '';
    let plainTextComplete = '';
    let templateHtmlComplete = '';

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      
      // Process complete SSE messages
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // Keep incomplete line in buffer

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const eventData = line.slice(6);
          
          if (eventData === '[DONE]') {
            // Stream completed
            return;
          }

          try {
            const event = JSON.parse(eventData);
            
            switch (event.type) {
              case 'status':
                onStatus?.(event.content, event.phase);
                break;
                
              case 'plain-text-chunk':
                plainTextComplete += event.content;
                onPlainTextChunk?.(event.content);
                break;
                
              case 'template-chunk':
                onTemplateChunk?.(event.content);
                break;
                
              case 'complete':
                const completeData = {
                  plainText: event.metadata.plainText,
                  templateHtml: event.metadata.templateHtml,
                  templateId: event.metadata.templateId,
                  letterId: event.metadata.letterId
                };
                
                // Save the completed letter
                const { letterId: savedLetterId, error } = await saveStreamedLetter(
                  completeData.plainText,
                  completeData.templateHtml,
                  completeData.templateId,
                  event.metadata.isEdit,
                  event.metadata.letterId
                );
                
                if (error) {
                  onError?.(error);
                  return;
                }
                
                onComplete?.({
                  ...completeData,
                  letterId: savedLetterId || completeData.letterId
                });
                break;
                
              case 'error':
                onError?.(event.content);
                return;
            }
          } catch (e) {
            console.error('Error parsing SSE data:', e);
          }
        }
      }
    }
  } catch (error) {
    console.error('Unified streaming error:', error);
    onError?.(error instanceof Error ? error.message : 'Unknown streaming error');
  }
}

/**
 * Legacy function for template-only streaming (kept for backward compatibility)
 */
export async function streamTemplateGeneration(
  plainText: string,
  templateHtml: string,
  templateId: string,
  letterId?: string,
  userId?: string,
  options?: StreamingOptions,
  isEdit?: boolean
): Promise<void> {
  const { onChunk, onComplete, onError } = options || {};

  try {
    const response = await authFetch('/api/edge/generate-application-letter', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        plainText,
        templateHtml,
        templateId,
        letterId,
        userId,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error('No response body');
    }

    let buffer = '';
    let completeHtml = '';

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      
      // Process complete SSE messages
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // Keep incomplete line in buffer

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            // Stream completed
            return;
          }

          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'chunk' && parsed.text) {
              onChunk?.(parsed.text);
            } else if (parsed.type === 'complete' && parsed.html) {
              completeHtml = parsed.html;
              // Persist the finished letter and obtain its ID
              const { letterId: savedLetterId, error } = await saveStreamedLetter(plainText, parsed.html, templateId, isEdit, letterId);
              if (error) {
                onError?.(error);
                return;
              }
              onComplete?.(parsed.html, savedLetterId || letterId);
            } else if (parsed.type === 'error' && parsed.error) {
              onError?.(parsed.error);
              return;
            }
          } catch (e) {
            console.error('Error parsing SSE data:', e);
          }
        }
      }
    }
  } catch (error) {
    console.error('Streaming error:', error);
    onError?.(error instanceof Error ? error.message : 'Unknown streaming error');
  }
}

/**
 * Prepares file data for unified streaming
 */
export async function prepareFileForStreaming(
  file: File
): Promise<{ data: string; mimeType: string }> {
  const mimeType = getMimeTypeFromFileName(file.name);
  
  if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    // Extract text from DOCX
    const buffer = await file.arrayBuffer();
    const text = await extractTextFromDocx(buffer);
    return { data: text, mimeType: 'text/plain' };
  } else if (mimeType === 'text/plain') {
    // Read as text
    const text = await file.text();
    return { data: text, mimeType };
  } else {
    // Convert to base64 for PDFs and images
    const base64 = await fileToBase64(file);
    return { data: base64, mimeType };
  }
}

/**
 * Convenience function to start unified streaming from files
 */
export async function streamLetterFromFiles(
  resumeFile: File,
  jobDescription: string,
  templateId: string,
  templateHtml: string,
  jobImageFile?: File,
  options?: UnifiedStreamingOptions
): Promise<void> {
  // Prepare resume data
  const resumeData = await prepareFileForStreaming(resumeFile);
  
  // Prepare job image data if provided
  let jobImageBase64: string | undefined;
  if (jobImageFile) {
    jobImageBase64 = await fileToBase64(jobImageFile);
  }
  
  // Create streaming data
  const streamingData: UnifiedStreamingData = {
    resumeData: resumeData.data,
    resumeMimeType: resumeData.mimeType,
    jobDescription,
    templateId,
    templateHtml,
    jobImage: jobImageBase64
  };
  
  // Start streaming
  return streamUnifiedLetterGeneration(streamingData, options || {});
}

/**
 * Save the completed letter to the database after streaming
 */
export async function saveStreamedLetter(
  plainText: string,
  designHtml: string | null,
  templateId: string,
  isEdit?: boolean,
  existingLetterId?: string
): Promise<{ letterId?: string; error?: string }> {
  try {
    const response = await authFetch('/api/letters/save-streamed', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        plainText,
        designHtml,
        templateId,
        isEdit,
        letterId: existingLetterId,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to save letter');
    }

    const data = await response.json();
    return { letterId: data.letterId };
  } catch (error) {
    console.error('Error saving streamed letter:', error);
    return { error: error instanceof Error ? error.message : 'Failed to save letter' };
  }
}