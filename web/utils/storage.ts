import { createClient } from "@/lib/supabase-server";

/**
 * Upload a PDF buffer to Supabase Storage and return a public URL.
 * Assumes a bucket named `resume_pdfs` with public permissions or RLS that allows read.
 */
export async function uploadPdf(id: string, pdf: Uint8Array): Promise<string> {
  const supabase = await createClient();

  const filePath = `${id}.pdf`;

  // Upload (upsert) the file
  const { error } = await supabase.storage.from("resume_pdfs").upload(filePath, pdf, {
    contentType: "application/pdf",
    upsert: true,
  });
  if (error) {
    throw new Error(`Storage upload failed: ${error.message}`);
  }

  // Get public URL
  const { data } = supabase.storage.from("resume_pdfs").getPublicUrl(filePath);
  return data.publicUrl;
}
