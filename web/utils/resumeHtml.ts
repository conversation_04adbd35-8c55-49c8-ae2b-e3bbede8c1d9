import { openai } from "@/utils/openaiClient";

/**
 * Generates a resume preview HTML using AI. Falls back to a bare-bones template if the
 * model or API key is unavailable.
 */
export async function generateResumeHtml(formData: any): Promise<string> {
  if (!openai) {
    throw new Error("AI client not configured (OPENAI_API_KEY missing)");
  }

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini", // pick a relatively cheap model; adjust if needed
      messages: [
        {
          role: "system",
          content:
            "You are a helpful assistant that converts structured resume data (JSON) into an elegant, ATS-friendly resume. Respond ONLY with valid HTML (inline CSS ok) without triple backticks.",
        },
        {
          role: "user",
          content: `Here is the JSON data for the resume: ${JSON.stringify(
            formData
          )}`,
        },
      ],
      temperature: 0.3,
      max_tokens: 1500,
    });

    const html = completion.choices[0]?.message?.content?.trim();
    if (html && html.startsWith("<")) {
      return html;
    }
    throw new Error("AI did not return valid HTML");
  } catch (err) {
    console.error("OpenAI resume HTML generation failed", err);
    throw err;
  }
}

// Legacy fallback template kept for reference but unused.
/*
function fallbackHtml(data: any): string {
  return `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Resume</title>
    <style>
      body { font-family: Arial, sans-serif; margin: 40px; }
      h1 { font-size: 24px; margin-bottom: 4px; }
      h2 { font-size: 18px; margin-top: 24px; margin-bottom: 4px; }
      .section { margin-top: 16px; }
    </style>
  </head>
  <body>
    <h1>${data.fullName ?? "Unnamed"}</h1>
    <div>${data.email ?? ""} | ${data.phone ?? ""}</div>

    <div class="section">
      <h2>Summary</h2>
      <p>${data.summary ?? "Professional summary goes here."}</p>
    </div>

    <div class="section">
      <h2>Experience</h2>
      <p>${data?.mostRecentJob?.position ?? "Position"} at ${
    data?.mostRecentJob?.company ?? "Company"
  }</p>
      <p>${data?.mostRecentJob?.description ?? "Job description."}</p>
    </div>

    <div class="section">
      <h2>Skills</h2>
      <p>${Array.isArray(data.skills) ? data.skills.join(", ") : ""}</p>
    </div>
  </body>
</html>`;
}
*/
