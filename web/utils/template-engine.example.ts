/**
 * Example usage of the template engine
 * This file demonstrates how to use the template engine utility
 */

import { fillResumeTemplate, clearTemplateCache, testTemplate, preCompileTemplates } from './template-engine';
import { cleanProfessionalTemplate, resumeTemplates } from './resume-templates/resumeTemplates';
import { StructuredResumeData } from '../types/resume-structured';

// Sample structured resume data for testing
const sampleResumeData: StructuredResumeData = {
  personalInfo: {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    linkedin: 'linkedin.com/in/johndoe',
    website: 'johndoe.dev',
    github: 'github.com/johndoe'
  },
  professionalSummary: 'Experienced software engineer with 5+ years of experience in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable web applications and leading cross-functional teams.',
  targetPosition: 'Senior Software Engineer',
  experiences: [
    {
      id: 'exp-1',
      jobTitle: 'Software Engineer',
      company: 'Tech Corp',
      location: 'San Francisco, CA',
      startDate: '2020-01-01',
      endDate: 'Present',
      responsibilities: [
        'Developed and maintained React-based web applications serving 100k+ users',
        'Implemented RESTful APIs using Node.js and Express.js',
        'Collaborated with cross-functional teams to deliver features on time',
        'Optimized application performance, reducing load times by 30%'
      ]
    },
    {
      id: 'exp-2',
      jobTitle: 'Junior Developer',
      company: 'StartupXYZ',
      location: 'San Francisco, CA',
      startDate: '2018-06-01',
      endDate: '2019-12-31',
      responsibilities: [
        'Built responsive web interfaces using HTML, CSS, and JavaScript',
        'Worked with senior developers to implement new features',
        'Participated in code reviews and agile development processes'
      ]
    }
  ],
  education: [
    {
      id: 'edu-1',
      degree: 'Bachelor of Science in Computer Science',
      institution: 'University of California, Berkeley',
      location: 'Berkeley, CA',
      graduationDate: '2018-05-15',
      gpa: '3.8',
      relevantCoursework: ['Data Structures', 'Algorithms', 'Software Engineering'],
      honors: ['Dean\'s List', 'Magna Cum Laude']
    }
  ],
  skills: {
    categories: [
      {
        category: 'Programming Languages',
        skills: ['JavaScript', 'TypeScript', 'Python', 'Java']
      },
      {
        category: 'Frameworks & Libraries',
        skills: ['React', 'Node.js', 'Express.js', 'Next.js']
      },
      {
        category: 'Tools & Technologies',
        skills: ['Git', 'Docker', 'AWS', 'MongoDB', 'PostgreSQL']
      }
    ]
  },
  certifications: [
    {
      id: 'cert-1',
      name: 'AWS Certified Developer',
      issuer: 'Amazon Web Services',
      date: '2023-03-15',
      credentialId: 'AWS-DEV-123456'
    }
  ],
  projects: [
    {
      id: 'proj-1',
      title: 'E-commerce Platform',
      description: 'Full-stack e-commerce application with React frontend and Node.js backend',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe API'],
      link: 'https://github.com/johndoe/ecommerce-platform',
      achievements: [
        'Implemented secure payment processing',
        'Built admin dashboard for inventory management'
      ]
    }
  ],
  languages: [
    {
      language: 'English',
      proficiency: 'Native'
    },
    {
      language: 'Spanish',
      proficiency: 'Conversational'
    }
  ]
};

/**
 * Example 1: Basic template filling
 */
export function basicTemplateExample() {
  try {
    console.log('=== Basic Template Example ===');
    
    // Fill template with sample data
    const html = fillResumeTemplate(cleanProfessionalTemplate, sampleResumeData);
    
    console.log('✅ Template filled successfully');
    console.log(`📄 Generated HTML length: ${html.length} characters`);
    console.log(`🎯 Template used: ${cleanProfessionalTemplate.name}`);
    
    // Basic validation
    const containsName = html.includes(sampleResumeData.personalInfo.fullName);
    const containsEmail = html.includes(sampleResumeData.personalInfo.email);
    const containsCompany = html.includes(sampleResumeData.experiences[0].company);
    
    console.log(`🔍 Validation - Name: ${containsName ? '✅' : '❌'}`);
    console.log(`🔍 Validation - Email: ${containsEmail ? '✅' : '❌'}`);
    console.log(`🔍 Validation - Company: ${containsCompany ? '✅' : '❌'}`);
    
    return html;
  } catch (error) {
    console.error('❌ Template filling failed:', error);
    throw error;
  }
}

/**
 * Example 2: Testing template compilation
 */
export function testTemplateExample() {
  console.log('\n=== Template Testing Example ===');
  
  // Test the clean professional template
  const testResult = testTemplate(cleanProfessionalTemplate, sampleResumeData);
  
  if (testResult.success) {
    console.log('✅ Template test passed');
    console.log(`📄 Generated HTML length: ${testResult.htmlLength} characters`);
    
    if (testResult.warnings && testResult.warnings.length > 0) {
      console.log('⚠️  Warnings:');
      testResult.warnings.forEach(warning => console.log(`   - ${warning}`));
    }
  } else {
    console.log('❌ Template test failed');
    console.log(`💥 Error: ${testResult.error}`);
  }
  
  return testResult;
}

/**
 * Example 3: Pre-compile templates for performance
 */
export function preCompileExample() {
  console.log('\n=== Pre-compile Templates Example ===');
  
  // Clear cache first
  clearTemplateCache();
  
  // Pre-compile all templates
  const compiledCount = preCompileTemplates(resumeTemplates);
  
  console.log(`📦 Pre-compiled ${compiledCount}/${resumeTemplates.length} templates`);
  
  // Now filling templates should be faster
  const startTime = Date.now();
  fillResumeTemplate(cleanProfessionalTemplate, sampleResumeData);
  const endTime = Date.now();
  
  console.log(`⚡ Template filling took ${endTime - startTime}ms (with cache)`);
  
  return compiledCount;
}

/**
 * Example 4: Error handling
 */
export function errorHandlingExample() {
  console.log('\n=== Error Handling Example ===');
  
  // Create incomplete data to trigger validation errors
  const incompleteData: StructuredResumeData = {
    personalInfo: {
      fullName: '', // Missing required field
      email: 'invalid-email', // Invalid email
      phone: '123', // Too short
      location: 'Test Location'
    },
    professionalSummary: 'Short', // Too short
    targetPosition: 'Test Position',
    experiences: [], // Empty array
    education: [], // Empty array
    skills: {
      categories: [] // Empty array
    }
  };
  
  try {
    fillResumeTemplate(cleanProfessionalTemplate, incompleteData);
    console.log('❌ Should have failed but didn\'t');
  } catch (error) {
    console.log('✅ Correctly caught validation error');
    console.log(`💥 Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Run all examples
 */
export function runAllExamples() {
  console.log('🚀 Running Template Engine Examples...\n');
  
  try {
    basicTemplateExample();
    testTemplateExample();
    preCompileExample();
    errorHandlingExample();
    
    console.log('\n✅ All examples completed successfully!');
  } catch (error) {
    console.error('\n❌ Example execution failed:', error);
  }
}

// Export for potential use in other files
export { sampleResumeData };