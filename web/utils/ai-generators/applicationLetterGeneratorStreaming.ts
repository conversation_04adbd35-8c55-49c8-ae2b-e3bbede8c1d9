import { GoogleGenAI, Part } from '@google/genai';
import { captureApiError } from '@/utils/errorMonitoring';
import { applicationLetterTemplates } from '@/utils/letter-templates/applicationLetterTemplates';
import { createClient } from '@/lib/supabase-server';
import { extractTextFromDocx } from '@/utils/docxUtils';

// Initialize the Google Generative AI client
const googleAI = new GoogleGenAI({apiKey: process.env.NEXT_PUBLIC_GOOGLE_AI_API_KEY || ''});

/**
 * Converts a file to a generative part for the AI model
 * @param file - The file buffer to convert
 * @param mimeType - The MIME type of the file
 * @returns Promise with the generative part
 */
async function fileToGenerativePart(file: ArrayBuffer, mimeType: string): Promise<Part> {
  const uint8Array = new Uint8Array(file);
  return {
    inlineData: {
      data: Buffer.from(uint8Array).toString('base64'),
      mimeType
    }
  };
}


export interface StreamingLetterGenerationResult {
  plainText: string;
  templateHtml?: string;
  templateId: string;
  letterId?: string;
  price?: number;
  currency?: string;
  isEdit?: boolean;
}

/**
 * Generate plain text letter content using Google AI
 * This is Phase 1 of the streaming process - generates the plain text
 */
export async function generatePlainTextLetter(
  resumeFile: { buffer: ArrayBuffer, mimeType: string },
  jobDescription?: string,
  jobImage?: { buffer: ArrayBuffer, mimeType: string },
  editedLetterText?: string,
): Promise<string> {
  try {
    if (editedLetterText) {
      // If edited text is provided, use it directly
      return editedLetterText;
    }

    // Validate inputs
    if (!['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'image/png', 'image/jpeg', 'image/jpg'].includes(resumeFile.mimeType)) {
      throw new Error('Unsupported resume file format. Please use PDF, DOCX, PNG, JPG, or JPEG files.');
    }

    if (!jobDescription && !jobImage) {
      throw new Error('Either job description text or job posting image must be provided');
    }

    let resumePart: Part;
    let jobImagePart: Part | undefined;

    if (resumeFile.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      const extractedText = await extractTextFromDocx(resumeFile.buffer);
      resumePart = { text: extractedText };
    } else {
      resumePart = await fileToGenerativePart(resumeFile.buffer, resumeFile.mimeType);
    }

    if (jobImage) {
      if (!jobImage.mimeType.startsWith('image/')) {
        throw new Error('Unsupported job image format. Only image formats are accepted.');
      }
      jobImagePart = await fileToGenerativePart(jobImage.buffer, jobImage.mimeType);
    }

    const date = new Date().toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      timeZone: 'Asia/Jakarta',
    });

    const prompt = `
You are a professional job application letter writer. You will receive a resume and a job description with a specific job position title. Craft a plain-text job application letter (surat lamaran pekerjaan) in Bahasa Indonesia that is formal. Do not include any markdown, bullet points, or headings; write only the letter text ready to copy-paste.

First, analyze the resume and job description thoroughly to identify:
1. Skills EXPLICITLY mentioned in the resume that match the job requirements
2. Skills IMPLICITLY suggested by the resume
3. Related skills in the resume that could TRANSFER to the job requirements
4. Education, certifications, and relevant experience from the resume

Then, write a formal application letter that:
- Uses proper Indonesian business letter format
- The character count of the entire letter (including spaces) should be around 1300-1600 characters, not more, not less
- Includes current date (${date}) at the top
- Addresses the HR department of the company. If the company's address is known, include it; otherwise, omit the address line.
- States the purpose of applying for the specific position clearly
- Highlights the candidate's relevant education, skills, and experiences
- Shows enthusiasm for the position and desire to contribute to the company
- Includes a polite closing asking for interview opportunity
- Uses a formal closing salutation

Job Information: ${jobDescription ? jobDescription : '[A job posting image is provided. Please analyze it to identify the job requirements, responsibilities, and qualifications]'}

Guidelines:
1. Use a formal and respectful tone appropriate for Indonesian business correspondence.
2. Include proper letter components IN ORDER: date, subject line (Perihal: Lamaran Pekerjaan sebagai [position]), recipient, body, and closing.
3. Be reasonable about implied skills from the resume.
4. EXTREMELY IMPORTANT: NEVER use phrases like "belum memiliki pengalaman" or mention any lack of experience.
5. Focus ONLY on the candidate's strengths and relevant experience.
6. If there are significant skill gaps, express enthusiasm for continuing professional development.
7. Reference the attached resume/CV for more detailed information.
8. Follow standard Indonesian formal letter structure.
9. The ENTIRE letter (including spaces) should be around 1300-1600 characters, not more, not less. Make it concise, but still formal and complete.

Return only the plain-text application letter without any additional commentary or manual edits.
    `;

    const response = await googleAI.models.generateContent({
      model: "gemini-2.0-flash",
      contents: [
        {
          role: "user",
          parts: [
            { text: prompt },
            resumePart,
            ...(jobImagePart ? [jobImagePart] : [])
          ]
        }
      ],
      config: {
        temperature: 0.2,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048,
      },
    });

    return response.text || '';
  } catch (error) {
    console.error('Error generating plain text letter:', error);
    captureApiError('generate-plain-text-letter', error);
    throw new Error('Failed to generate plain text letter');
  }
}

/**
 * Prepare data for streaming template generation
 * This prepares the data to be sent to the Edge Function for streaming
 */
export async function prepareStreamingData(
  plainText: string,
  templateId: string,
  userId?: string,
  isEdit?: boolean,
  existingLetterId?: string
): Promise<StreamingLetterGenerationResult> {
  // Get the selected template by ID
  const selectedTemplate = applicationLetterTemplates.find(template => template.id === templateId);
  
  // For plain text option, save directly and return
  if (!selectedTemplate) {
    let letterId: string | undefined = existingLetterId;

    if (userId) {
      try {
        const supabase = await createClient();

        if (existingLetterId && isEdit) {
          // Update existing letter
          const { data: letterData, error: saveError } = await supabase
            .from('letters')
            .update({
              plain_text: plainText,
              design_html: null,
              template_id: templateId
            })
            .eq('id', existingLetterId)
            .eq('user_id', userId)
            .select('id')
            .single();

          if (saveError) {
            console.error('Error updating letter in database:', saveError);
            captureApiError('update-letter-to-database', saveError);
          } else {
            letterId = letterData?.id;
          }
        } else {
          // Create new letter
          const { data: letterData, error: saveError } = await supabase
            .from('letters')
            .insert({
              user_id: userId,
              plain_text: plainText,
              design_html: null,
              template_id: templateId
            })
            .select('id')
            .single();

          if (saveError) {
            console.error('Error saving letter to database:', saveError);
            captureApiError('save-letter-to-database', saveError);
          } else {
            letterId = letterData?.id;
          }
        }
      } catch (dbError) {
        console.error('Database error when saving/updating letter:', dbError);
        captureApiError('database-save-letter', dbError as Error);
      }
    }

    return {
      plainText,
      templateId,
      letterId,
      price: undefined,
      currency: undefined,
      isEdit
    };
  }

  // Return data for streaming template generation
  return {
    plainText,
    templateHtml: selectedTemplate.templateHtml,
    templateId,
    letterId: existingLetterId, // Pass through existing letterId for updates
    price: 15000, // Fixed price for letter generation
    currency: 'IDR',
    isEdit
  };
}

/**
 * Save the completed letter to database after streaming is done
 * This should be called from the client after receiving the complete streamed response
 */
export async function saveCompletedLetter(
  userId: string,
  plainText: string,
  designHtml: string | null,
  templateId: string
): Promise<string | undefined> {
  try {
    const supabase = await createClient();
    
    const { data: letterData, error: saveError } = await supabase
      .from('letters')
      .insert({
        user_id: userId,
        plain_text: plainText,
        design_html: designHtml,
        template_id: templateId
      })
      .select('id')
      .single();

    if (saveError) {
      console.error('Error saving letter to database:', saveError);
      captureApiError('save-letter-to-database', saveError);
      return undefined;
    }

    return letterData?.id;
  } catch (dbError) {
    console.error('Database error when saving letter:', dbError);
    captureApiError('database-save-letter', dbError as Error);
    return undefined;
  }
}