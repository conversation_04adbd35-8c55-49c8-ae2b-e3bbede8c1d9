'use client';

import Link from 'next/link';
import { Nunito } from 'next/font/google';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import NavLink from './NavLink';

const nunito = Nunito({ subsets: ['latin'], weight: '700' });

// Array navigasi untuk digunakan di desktop dan mobile view
const navigationItems = [
  { href: '/email-application', label: 'Em<PERSON> Lamaran' },
  { href: '/application-letter', label: 'Surat Lamaran' },
  { href: '/job-match', label: 'Kecocokan Lowongan' },
];

type NavbarProps = {
  auth: ReturnType<typeof useAuth>;
};

export default function Navbar({ auth }: NavbarProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();
  const { user, loading, signOut, profile, profileLoading } = auth;

  return (
    <nav className="bg-white shadow-md fixed top-0 w-full z-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-1">
        <div className="flex justify-between h-16">
          {/* Logo Section */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/" className="flex items-center">
              <img src="/images/logo.svg" alt="Gigsta Logo" className="h-16 invert" />
              <span className={`ml-2 text-2xl font-bold text-gray-900 tracking-tight ${nunito.className}`}>Gigsta</span>
            </Link>
          </div>

          {/* Navigation Section - Center */}
          <div className="flex-1 flex justify-center items-center max-w-md mx-8">
            <div className="hidden lg:flex lg:space-x-6">
              {navigationItems.map((item) => (
                <NavLink key={item.href} href={item.href}>
                  {item.label}
                </NavLink>
              ))}
            </div>
          </div>

          {/* User Actions Section - Right */}
          <div className="hidden lg:flex lg:items-center lg:space-x-3">
            {!loading && (
              <>
                {user ? (
                  <>
                    {/* Token Display */}
                    <Link href="/buy-tokens" className="flex items-center bg-amber-50 border border-amber-200 text-amber-700 px-3 py-2 rounded-lg hover:bg-amber-100 transition-colors cursor-pointer">
                      <svg className="w-4 h-4 text-amber-600 mr-1" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
                        <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
                        <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
                        <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
                      </svg>
                      <span className="text-sm font-medium text-amber-800">
                        {profileLoading ? '...' : (profile?.tokens ?? '-')}
                      </span>
                      <svg className="w-3 h-3 text-amber-600 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v14m7-7H5" />
                      </svg>
                    </Link>
                    <Link
                      href="/profile"
                      className="bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-primary text-gray-700 hover:text-primary px-3 py-2 rounded-lg text-sm font-medium flex items-center transition-all duration-200"
                    >
                      <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mr-2">
                        <svg className="w-3 h-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                      </div>
                      <span className="text-sm font-medium text-gray-900 truncate max-w-24">{user.email}</span>
                    </Link>
                    <button
                      onClick={async () => {
                        const result = await signOut();
                        if (result.success) {
                          window.location.href = '/';
                        }
                      }}
                      className="bg-red-50 hover:bg-red-100 border border-red-200 hover:border-red-300 text-red-600 hover:text-red-700 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center"
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                      </svg>
                      Keluar
                    </button>
                  </>
                ) : (
                  <>
                    <Link href="/login" className="text-gray-500 hover:text-primary px-3 py-2 rounded-md text-sm lg:text-base font-medium">
                      Masuk
                    </Link>
                    <Link href="/register" className="btn-primary text-sm lg:text-base">
                      Daftar
                    </Link>
                  </>
                )}
              </>
            )}
          </div>

          <div className="flex items-center lg:hidden">
            {/* Token Display - Mobile */}
            {!loading && user && (
              <div className="mx-2">
                <Link href="/buy-tokens" className="flex items-center bg-amber-50 border border-amber-200 text-amber-700 px-3 py-2 rounded-lg hover:bg-amber-100 transition-colors cursor-pointer">
                  <svg className="w-4 h-4 text-amber-600 mr-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
                    <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
                    <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
                    <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
                  </svg>
                  <span className="text-sm font-medium text-amber-800">
                    {profileLoading ? '...' : (profile?.tokens ?? '-')}
                  </span>
                  <svg className="w-3 h-3 text-amber-600 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v14m7-7H5" />
                  </svg>
                </Link>
              </div>
            )}

            <div className="-mr-2">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
              >
                <span className="sr-only">Buka menu</span>
                {/* Icon for menu */}
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      {isMenuOpen && (
        <div className="lg:hidden">
          <div className="pt-2 pb-3 space-y-1">
            {navigationItems.map((item) => (
              <NavLink 
                key={item.href} 
                href={item.href} 
                mobileView={true}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.label}
              </NavLink>
            ))}
          </div>
          <div className="pt-4 pb-4 border-t border-gray-200">
            <div className="px-4 space-y-4">
              {!loading && (
                <>
                  {user ? (
                    <div className="space-y-3">
                      <Link
                        href="/profile"
                        className="bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-primary text-gray-700 hover:text-primary px-4 py-3 rounded-lg text-sm font-medium flex items-center transition-all duration-200 w-full"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-gray-500 mb-1">Profil Saya</div>
                          <div className="text-sm font-medium text-gray-900 truncate">{user.email}</div>
                        </div>
                        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      </Link>
                      <button
                        onClick={async () => {
                          const result = await signOut();
                          if (result.success) {
                            router.push('/');
                            router.refresh();
                            setIsMenuOpen(false);
                          }
                        }}
                        className="bg-red-50 hover:bg-red-100 border border-red-200 hover:border-red-300 text-red-600 hover:text-red-700 px-4 py-3 rounded-lg text-sm font-medium w-full text-center transition-all duration-200 flex items-center justify-center"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        Keluar
                      </button>
                    </div>
                  ) : (
                    <>
                      <Link 
                        href="/login" 
                        className="text-gray-500 hover:text-primary block px-3 py-2 rounded-md text-base font-medium"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Masuk
                      </Link>
                      <Link 
                        href="/register" 
                        className="btn-primary block text-center" 
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Daftar
                      </Link>
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
