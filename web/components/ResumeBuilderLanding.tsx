"use client";

import React from "react";

// Icon components
const BotIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
  </svg>
);

const PencilIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z" />
  </svg>
);

interface ResumeBuilderLandingProps {
  onSelectPath: (path: 'ai-powered' | 'manual') => void;
}

const ResumeBuilderLanding: React.FC<ResumeBuilderLandingProps> = ({ onSelectPath }) => {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Bagaimana Anda ingin membuat CV?</h2>
        <p className="text-gray-600 text-lg">Pilih metode yang paling sesuai dengan kebutuhan Anda</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {/* AI-Powered Resume Path */}
        <button
          onClick={() => onSelectPath('ai-powered')}
          className="group p-8 border-2 border-gray-200 rounded-2xl hover:border-primary hover:shadow-xl transition-all duration-300 text-left h-full"
        >
          <div className="flex flex-col items-center space-y-6">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <BotIcon className="w-10 h-10 text-white" />
            </div>
            
            <div className="space-y-3 text-center">
              <h3 className="text-2xl font-bold text-gray-900 group-hover:text-primary transition-colors">
                CV Spesifik Pekerjaan
              </h3>
              <p className="text-gray-600 group-hover:text-gray-700">
                Disesuaikan untuk pekerjaan tertentu dengan bantuan AI
              </p>
            </div>

            <div className="space-y-3 text-sm text-gray-500 group-hover:text-gray-600 w-full">
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Kata kunci yang dioptimalkan</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Disesuaikan dengan deskripsi pekerjaan</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Peluang wawancara lebih tinggi</span>
              </div>
            </div>

            <div className="w-full pt-4 border-t border-gray-100 text-center">
              <span className="text-primary font-semibold group-hover:underline">
                Mulai dengan AI →
              </span>
            </div>
          </div>
        </button>

        {/* Manual Resume Path */}
        <button
          onClick={() => onSelectPath('manual')}
          className="group p-8 border-2 border-gray-200 rounded-2xl hover:border-primary hover:shadow-xl transition-all duration-300 text-left h-full"
        >
          <div className="flex flex-col items-center space-y-6">
            <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <PencilIcon className="w-10 h-10 text-white" />
            </div>
            
            <div className="space-y-3 text-center">
              <h3 className="text-2xl font-bold text-gray-900 group-hover:text-primary transition-colors">
                Buat Manual
              </h3>
              <p className="text-gray-600 group-hover:text-gray-700">
                Kontrol penuh dengan editor CV yang canggih
              </p>
            </div>

            <div className="space-y-3 text-sm text-gray-500 group-hover:text-gray-600 w-full">
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Fleksibel untuk berbagai posisi</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Kontrol penuh atas konten</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Siap pakai kapan saja</span>
              </div>
            </div>

            <div className="w-full pt-4 border-t border-gray-100 text-center">
              <span className="text-primary font-semibold group-hover:underline">
                Mulai Manual →
              </span>
            </div>
          </div>
        </button>
      </div>

      <div className="text-center text-sm text-gray-500 max-w-2xl mx-auto">
        <p>
          <strong>CV Spesifik Pekerjaan:</strong> Unggah CV lama + masukkan deskripsi pekerjaan → AI membuat CV yang disesuaikan<br />
          <strong>Buat Manual:</strong> Langsung ke editor → Isi semua detail sendiri dengan bantuan template
        </p>
      </div>
    </div>
  );
};

export default ResumeBuilderLanding;