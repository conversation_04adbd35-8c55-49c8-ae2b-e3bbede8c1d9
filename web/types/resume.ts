// Define interface for resume generation input
export interface ResumeInput {
  // For uploaded resume file (can contain either buffer or extractedText)
  file?: {
    buffer?: ArrayBuffer;
    extractedText?: string;
    mimeType: string;
  };
  // For manual/scratch input
  manual?: {
    fullName: string;
    professionalTitle: string;
    professionalSummary: string;
    mostRecentJob: {
      title: string;
      company: string;
      achievements: string;
    };
    skills: string;
  };
}

// Define interface for job input
export interface JobInput {
  // Job description text
  description?: string;
  // Job posting image
  image?: {
    buffer: ArrayBuffer;
    mimeType: string;
  };
}