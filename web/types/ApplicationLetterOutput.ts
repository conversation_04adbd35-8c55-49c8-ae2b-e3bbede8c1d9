import { LetterDesignOutput } from '@/utils/letter-designs/letterDesignTypes';

/**
 * Generates an application letter using the gemini-2.0-flash model
 * @param resumeFile - The resume file buffer and mime type
 * @param jobDescription - The job description (optional if jobImage is provided)
 * @param jobImage - The job posting image file (optional if jobDescription is provided)
 * @returns The generated application letter text
 */
export interface ApplicationLetterOutput {
  plainText: string;
  design?: LetterDesignOutput;
  letterId?: string;
  price?: number;
  currency?: string;
}