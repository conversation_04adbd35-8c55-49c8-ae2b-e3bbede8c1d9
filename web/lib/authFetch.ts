/**
 * Utility function for making authenticated API requests
 * This ensures that the user's session token is passed in the request headers
 */

import { createClient } from './supabase';

/**
 * Makes an authenticated fetch request to an API endpoint
 * 
 * @param url The API endpoint URL
 * @param options Additional fetch options
 * @returns The fetch response
 */
export async function authFetch(url: string, options: RequestInit = {}): Promise<Response> {
  // Get the current Supabase session
  const supabase = createClient();
  const { data } = await supabase.auth.getSession();
  const session = data?.session;
  
  // If there's no session, proceed with an unauthenticated request
  if (!session) {
    console.warn('No active session found for authenticated request');
    return fetch(url, options);
  }
  
  // Get access token
  const accessToken = session.access_token;
  
  // Create headers with the access token
  const headers = new Headers(options.headers || {});
  headers.set('x-supabase-auth', accessToken);
  
  // Return fetch with the authentication headers
  return fetch(url, {
    ...options,
    headers,
  });
}

/**
 * Makes an authenticated GET request
 */
export async function authGet(url: string, options: RequestInit = {}): Promise<Response> {
  return authFetch(url, {
    ...options,
    method: 'GET'
  });
}

/**
 * Makes an authenticated POST request with FormData
 */
export async function authPost(url: string, body: Record<string, any>, options: RequestInit = {}): Promise<Response> {
  const formData = new FormData();
  
  // Append all body fields to FormData
  Object.entries(body).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      formData.append(key, value);
    }
  });
  
  // Don't set Content-Type header, let the browser set it with the correct boundary
  const { headers: _, ...restOptions } = options;
  
  return authFetch(url, {
    ...restOptions,
    method: 'POST',
    body: formData
  });
}

/**
 * Makes an authenticated DELETE request with JSON body
 */
export async function authDelete(url: string, body: any = {}, options: RequestInit = {}): Promise<Response> {
  const headers = new Headers(options.headers || {});
  headers.set('Content-Type', 'application/json');
  
  return authFetch(url, {
    ...options,
    method: 'DELETE',
    body: JSON.stringify(body),
    headers
  });
}

/**
 * Makes an authenticated POST request with FormData body
 */
export async function authPostFormData(url: string, formData: FormData, options: RequestInit = {}): Promise<Response> {
  return authFetch(url, {
    ...options,
    method: 'POST',
    body: formData
  });
}
