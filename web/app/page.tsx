'use client';

import Navbar from '@/components/Navbar';
import { useAuth } from '@/hooks/useAuth';
import Footer from '@/components/Footer';
import Seo from '@/components/Seo';
import Link from 'next/link';
import { useCallback } from 'react';

export default function Home() {
  const auth = useAuth();
  const scrollToFeatures = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);
  return (
    <main className="min-h-screen flex flex-col pt-16">
      <Seo 
        title="Buat Surat Lamaran Instan Berbasis AI"
        description="Optimalkan lamaran kerja Anda dengan AI. Buat surat lamaran kerja dan email lamaran kerja otomatis. Temukan pekerjaan impian <PERSON><PERSON> le<PERSON> cepat."
        canonical="https://gigsta.io"
      />
      <Navbar auth={auth} />

      {/* Sticky Banner for Unauthenticated Users */}
      {!auth.loading && !auth.user && (
        <div className="sticky top-[4.5rem] z-10 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white py-2 sm:py-3 px-4 shadow-lg">
          <div className="max-w-7xl mx-auto flex items-center justify-between gap-3">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                <div className="relative">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-300 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                  </svg>
                  <div className="absolute inset-0 w-4 h-4 sm:w-5 sm:h-5 bg-yellow-300 rounded-full opacity-20 animate-ping"></div>
                </div>
                <span className="font-bold text-sm sm:text-base">🎉</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2 min-w-0">
                <span className="font-bold text-xs sm:text-sm md:text-base leading-tight">
                  BONUS 10 TOKEN GRATIS!
                </span>
                <span className="text-xs sm:text-sm opacity-90 leading-tight">
                  untuk pengguna baru yang mendaftar
                </span>
              </div>
            </div>
            <Link
              href="/register"
              className="bg-white text-purple-600 hover:bg-yellow-100 font-bold py-1.5 px-3 sm:py-2 sm:px-4 rounded-full text-xs sm:text-sm text-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex-shrink-0"
            >
              Daftar Sekarang
            </Link>
          </div>
        </div>
      )}

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col items-center justify-center text-center">
            <div className="max-w-3xl">
              <h2 className="text-4xl md:text-5xl font-bold mb-4">
                Buat surat lamaran instan yang menarik perhatian HR
              </h2>
              <div className="mb-4">
                <span className="bg-yellow-300 text-blue-800 text-xs font-bold px-3 py-1 rounded-full inline-flex items-center animate-pulse">AI-Powered</span>
              </div>
              <p className="text-xl mb-8">
                Platform berbasis AI untuk membuat surat lamaran secara otomatis dan dipersonalisasi, mempercepat proses melamar dan meningkatkan peluang kerja
              </p>
              <div>
                <a 
                  href="#features" 
                  onClick={scrollToFeatures}
                  className="inline-block bg-white text-blue-700 hover:bg-blue-50 text-center px-8 py-3 text-lg font-bold rounded shadow-lg transition-colors cursor-pointer"
                >
                  Mulai Sekarang
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 bg-white scroll-mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Fitur Unggulan Kami</h2>
            <p className="mt-4 text-xl text-gray-600">Solusi berbasis kecerdasan buatan untuk membantu Anda mempercepat proses melamar pekerjaan impian</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Email Lamaran Feature */}
            <Link href="/email-application" className="block">
              <div className="border border-gray-200 rounded-lg p-6 bg-white shadow hover:shadow-lg transition-all cursor-pointer h-full">
                <div className="rounded-full bg-blue-100 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-center mb-2">Email Lamaran</h3>
                <p className="text-gray-600 text-center mb-4">
                  Email lamaran kerja berbasis AI yang disesuaikan otomatis dengan CV/resume dan deskripsi lowongan, siap digunakan untuk melamar kerja secara online.
                </p>
                <div className="text-center">
                  <span className="text-primary hover:text-blue-700 font-medium inline-block">
                    Buat Email Lamaran &rarr;
                  </span>
                </div>
              </div>
            </Link>
            
            {/* Application Letter Feature */}
            <Link href="/application-letter" className="block">
              <div className="border border-gray-200 rounded-lg p-6 bg-white shadow hover:shadow-lg transition-all cursor-pointer h-full">
                <div className="rounded-full bg-blue-100 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-center mb-2">Surat Lamaran</h3>
                <p className="text-gray-600 text-center mb-4">
                  Dokumen lamaran kerja profesional berbasis AI yang secara cerdas menyoroti pengalaman dan kualifikasi relevan sesuai dengan CV/resume dan deskripsi lowongan.
                </p>
                <div className="text-center">
                  <span className="text-primary hover:text-blue-700 font-medium inline-block">
                    Buat Surat Lamaran &rarr;
                  </span>
                </div>
              </div>
            </Link>
            
            {/* Job Match Feature */}
            <Link href="/job-match" className="block">
              <div className="border border-gray-200 rounded-lg p-6 bg-white shadow hover:shadow-lg transition-all cursor-pointer h-full">
                <div className="rounded-full bg-blue-100 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-center mb-2">Kecocokan Lowongan</h3>
                <p className="text-gray-600 text-center mb-4">
                  Analisis AI canggih yang membantu Anda melihat CV/resume dari sudut pandang perekrut dan memberikan saran untuk meningkatkan peluang diterima kerja.
                </p>
                <div className="text-center">
                  <span className="text-primary hover:text-blue-700 font-medium inline-block">
                    Cek Kecocokan &rarr;
                  </span>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </section>
      

      {/* How It Works Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Cara Kerja</h2>
            <p className="mt-4 text-xl text-gray-600">Tiga langkah sederhana untuk meningkatkan peluang karir Anda</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="flex flex-col items-center">
              <div className="bg-primary text-white rounded-full w-12 h-12 flex items-center justify-center mb-4 text-xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-center mb-2">Pilih Fitur</h3>
              <p className="text-gray-600 text-center">
                Pilih apakah Anda ingin membuat <strong>email lamaran</strong>, <strong>surat lamaran profesional</strong>, atau <strong>analisis kecocokan lowongan</strong> dari CV/resume Anda dengan lowongan pekerjaan.
              </p>
            </div>
            
            {/* Step 2 */}
            <div className="flex flex-col items-center">
              <div className="bg-primary text-white rounded-full w-12 h-12 flex items-center justify-center mb-4 text-xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-center mb-2">Masukkan Detail Lamaran</h3>
              <p className="text-gray-600 text-center">
                Upload CV/resume Anda dalam format PDF atau DOCX, dan masukkan informasi pekerjaan yang Anda minati atau deskripsi pekerjaan yang ingin dianalisis.
              </p>
            </div>
            
            {/* Step 3 */}
            <div className="flex flex-col items-center">
              <div className="bg-primary text-white rounded-full w-12 h-12 flex items-center justify-center mb-4 text-xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold text-center mb-2">Dapatkan Hasil</h3>
              <p className="text-gray-600 text-center">
                Dapatkan <strong>teks/dokumen lamaran</strong> yang siap digunakan untuk melamar pekerjaan atau <strong>analisis kecocokan</strong> yang dibuat AI untuk meningkatkan peluang diterima kerja.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Keunggulan Platform Kami</h2>
            <p className="mt-4 text-xl text-gray-600">Mengapa memilih Gigsta untuk kebutuhan karir Anda</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-10">
            {/* Benefit 1 */}
            <div className="flex">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-100 text-primary">
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">AI Canggih & Terkini</h3>
                <p className="mt-2 text-gray-600">
                  Menggunakan model AI terbaru untuk menganalisis CV/resume dan deskripsi pekerjaan, memberikan hasil yang akurat dan rekomendasi yang disesuaikan khusus untuk Anda.
                </p>
              </div>
            </div>
            
            {/* Benefit 2 */}
            <div className="flex">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-100 text-primary">
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">Hemat Waktu</h3>
                <p className="mt-2 text-gray-600">
                  Menghemat waktu Anda dengan mengotomatisasi pembuatan email lamaran, surat lamaran, dan analisis kecocokan lowongan dengan cepat dan efisien.
                </p>
              </div>
            </div>
            
            {/* Benefit 3 */}
            <div className="flex">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-100 text-primary">
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">Personalisasi Cerdas</h3>
                <p className="mt-2 text-gray-600">
                  Platform kami menganalisis keunikan pengalaman dan keahlian Anda untuk menghasilkan email lamaran, surat lamaran, dan rekomendasi yang dipersonalisasi sesuai dengan industri dan posisi target Anda.
                </p>
              </div>
            </div>
            
            {/* Benefit 4 */}
            <div className="flex">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-100 text-primary">
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">Surat Lamaran Unik</h3>
                <p className="mt-2 text-gray-600">
                  <strong className="text-primary">Tingkatkan peluang diterima hingga 3x lipat!</strong> Rekruter cenderung mengabaikan surat lamaran generik. Lamaran yang disesuaikan dengan posisi spesifik mendapatkan respon lebih tinggi dari perusahaan.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 bg-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Siap Meningkatkan Peluang Karir Anda?</h2>
          <p className="text-xl mb-6 max-w-3xl mx-auto">
            Manfaatkan teknologi AI untuk membuat email lamaran yang persuasif, surat lamaran yang profesional, dan analisis kecocokan lowongan kerja yang akurat.
          </p>
          <p className="text-lg mb-8 max-w-3xl mx-auto font-medium">
            <span className="bg-blue-700 px-2 py-1 rounded">💡 Pro Tip:</span> Rekruter menghabiskan rata-rata hanya 7 detik untuk meninjau CV. Surat lamaran yang disesuaikan khusus untuk setiap posisi dapat meningkatkan peluang wawancara Anda hingga 3x lipat!
          </p>
          <a 
            href="#features" 
            onClick={scrollToFeatures}
            className="bg-white text-primary hover:bg-gray-100 font-bold py-3 px-6 rounded-lg transition-colors inline-block cursor-pointer"
          >
            Mulai Sekarang
          </a>
        </div>
      </section>
      
      <Footer />
    </main>
  );
}
