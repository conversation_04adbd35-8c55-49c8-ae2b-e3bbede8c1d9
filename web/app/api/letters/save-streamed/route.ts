import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { createApi<PERSON>andler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';

async function handleRequest(request: NextRequest) {
  try {
    const { plainText, designHtml, templateId, isEdit, letterId } = await request.json();
    
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    if (!accessToken) {
      return NextResponse.json({ error: 'Missing authentication token' }, { status: 401 });
    }

    // Create Supabase client
    const supabase = await createClient();

    let userId: string | undefined;

    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);
    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    userId = user.id;

    let letterData;
    let saveError;

    // If letterId is provided and this is an edit, update the existing letter
    if (letterId && isEdit) {
      const { data, error } = await supabase
        .from('letters')
        .update({
          plain_text: plainText,
          design_html: designHtml,
          template_id: templateId
        })
        .eq('id', letterId)
        .eq('user_id', userId) // Ensure user can only update their own letters
        .select('id')
        .single();

      letterData = data;
      saveError = error;
    } else {
      // Create a new letter
      const { data, error } = await supabase
        .from('letters')
        .insert({
          user_id: userId,
          plain_text: plainText,
          design_html: designHtml,
          template_id: templateId
        })
        .select('id')
        .single();

      letterData = data;
      saveError = error;
    }
    
    if (saveError) {
      console.error('Error saving/updating streamed letter:', saveError);
      captureApiError('save-streamed-letter', saveError);
      return NextResponse.json({
        error: letterId && isEdit ? 'Failed to update letter' : 'Failed to save letter'
      }, { status: 500 });
    }

    // After saving the letter, decrement the user's token balance (only for new letters, not edits)
    if (!isEdit) {
      try {
        // Fetch current token balance
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('tokens')
          .eq('id', userId)
          .single();

        if (profileError) {
          console.error('Error fetching user tokens:', profileError);
        } else if (profileData) {
          const currentTokens = profileData.tokens ?? 0;
          // Determine token cost of the template (default to 0 if not found)
          const template = getTemplateById(templateId);
          const tokenCost = template?.tokenCost ?? 0;
          const newTokens = Math.max(currentTokens - tokenCost, 0);
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ tokens: newTokens })
            .eq('id', userId);
          if (updateError) {
            console.error('Error decrementing user tokens:', updateError);
          } else {
            console.log(`Successfully deducted ${tokenCost} tokens from user ${userId}. New balance: ${newTokens}`);
          }
        }
      } catch (tokenError) {
        console.error('Unexpected error while decrementing tokens:', tokenError);
      }
    } else {
      console.log(`Skipping token deduction for edit operation for user ${userId}`);
    }
    
    return NextResponse.json({
      success: true,
      letterId: letterData?.id
    });
    
  } catch (error) {
    console.error('Error in save-streamed endpoint:', error);
    captureApiError('save-streamed-letter', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to save letter'
    }, { status: 500 });
  }
}

export const POST = createApiHandler('save-streamed-letter', handleRequest);