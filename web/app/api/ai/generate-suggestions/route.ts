import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);

interface GenerateSuggestionsRequest {
  fieldKey: string;
  currentValue: string;
  context: {
    fieldType: string;
    personalInfo: any;
    targetPosition: string;
    professionalSummary: string;
    skills?: any;
    experiences?: any;
    education?: any;
    projects?: any;
    certifications?: any;
    languages?: any;
    awards?: any;
  };
  fieldType: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: GenerateSuggestionsRequest = await request.json();
    const { fieldKey, currentValue, context, fieldType } = body;

    if (!process.env.GOOGLE_AI_API_KEY) {
      return NextResponse.json(
        { error: 'Google AI API key not configured' },
        { status: 500 }
      );
    }

    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash-lite' });

    // Generate contextual prompt based on field type and key
    const prompt = generatePrompt(fieldKey, currentValue, context, fieldType);

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    console.log(text);

    // Parse the response to extract suggestions
    const suggestions = parseAISuggestions(text);

    return NextResponse.json({
      suggestions,
      fieldKey,
    });

  } catch (error) {
    console.error('Error generating AI suggestions:', error);
    return NextResponse.json(
      { error: 'Failed to generate suggestions' },
      { status: 500 }
    );
  }
}

function generatePrompt(fieldKey: string, currentValue: string, context: any, fieldType: string): string {
  const { personalInfo, targetPosition, professionalSummary, skills, experiences, education, projects, certifications, languages, awards } = context;
  
  // Helper function to format array data for context
  const formatArrayData = (data: any, type: string) => {
    switch (type) {
      case 'experiences':
        if (!data || data.length === 0) return 'None provided';
        return data.map((exp: any, i: number) => `${i + 1}. ${exp.jobTitle} at ${exp.company}`).join(', ');
      case 'education':
        if (!data || data.length === 0) return 'None provided';
        return data.map((edu: any, i: number) => `${i + 1}. ${edu.degree} from ${edu.institution}`).join(', ');
      case 'skills':
        if (!data || !data.categories || data.categories.length === 0) return 'None provided';
        return data.categories.map((cat: any, i: number) => `${i + 1}. ${cat.category}: ${cat.skills?.join(', ') || 'None'}`).join('; ');
      case 'projects':
        if (!data || data.length === 0) return 'None provided';
        return data.map((proj: any, i: number) => `${i + 1}. ${proj.title}`).join(', ');
      case 'certifications':
        if (!data || data.length === 0) return 'None provided';
        return data.map((cert: any, i: number) => `${i + 1}. ${cert.name} from ${cert.issuer}`).join(', ');
      case 'languages':
        if (!data || data.length === 0) return 'None provided';
        return data.map((lang: any, i: number) => `${i + 1}. ${lang.language} (${lang.proficiency})`).join(', ');
      case 'awards':
        if (!data || data.length === 0) return 'None provided';
        return data.map((award: any, i: number) => `${i + 1}. ${award.title} from ${award.issuer}`).join(', ');
      default:
        return 'None provided';
    }
  };

  let baseContext = `
Context Information:
- Target Position: ${targetPosition || 'Not specified'}
- Professional Summary: ${professionalSummary || 'Not provided'}
- Full Name: ${personalInfo?.fullName || 'Not provided'}
- Current Field Value: "${currentValue}"
- Work Experience: ${formatArrayData(experiences, 'experiences')}
- Education: ${formatArrayData(education, 'education')}
- Skills: ${formatArrayData(skills, 'skills')}
- Projects: ${formatArrayData(projects, 'projects')}
- Certifications: ${formatArrayData(certifications, 'certifications')}
- Languages: ${formatArrayData(languages, 'languages')}
- Awards: ${formatArrayData(awards, 'awards')}
`;

  // Helper functions to check if data contains meaningful content
  const hasContent = (value: string | undefined | null): boolean => {
    return value !== undefined && value !== null && value.trim().length > 0;
  };

  const hasExperienceContent = (experiences: any[]): boolean => {
    if (!experiences || experiences.length === 0) return false;
    return experiences.some(exp =>
      hasContent(exp.jobTitle) || hasContent(exp.company) || hasContent(exp.location) ||
      hasContent(exp.startDate) || hasContent(exp.endDate) ||
      (exp.responsibilities && exp.responsibilities.some((resp: string) => hasContent(resp)))
    );
  };

  const hasEducationContent = (education: any[]): boolean => {
    if (!education || education.length === 0) return false;
    return education.some(edu =>
      hasContent(edu.degree) || hasContent(edu.institution) || hasContent(edu.location) ||
      hasContent(edu.graduationDate) || hasContent(edu.gpa)
    );
  };

  const hasSkillsContent = (skills: any): boolean => {
    if (!skills || !skills.categories) return false;
    return skills.categories.some((cat: any) =>
      hasContent(cat.category) || (cat.skills && cat.skills.some((skill: string) => hasContent(skill)))
    );
  };

  const hasProjectsContent = (projects: any[]): boolean => {
    if (!projects || projects.length === 0) return false;
    return projects.some(proj =>
      hasContent(proj.title) || hasContent(proj.description) || hasContent(proj.link) ||
      (proj.technologies && proj.technologies.some((tech: string) => hasContent(tech))) ||
      (proj.achievements && proj.achievements.some((ach: string) => hasContent(ach)))
    );
  };

  const hasCertificationsContent = (certifications: any[]): boolean => {
    if (!certifications || certifications.length === 0) return false;
    return certifications.some(cert =>
      hasContent(cert.name) || hasContent(cert.issuer) || hasContent(cert.date) || hasContent(cert.credentialId)
    );
  };

  const hasLanguagesContent = (languages: any[]): boolean => {
    if (!languages || languages.length === 0) return false;
    return languages.some(lang =>
      hasContent(lang.language) || hasContent(lang.proficiency)
    );
  };

  const hasAwardsContent = (awards: any[]): boolean => {
    if (!awards || awards.length === 0) return false;
    return awards.some(award =>
      hasContent(award.title) || hasContent(award.issuer) || hasContent(award.date) || hasContent(award.description)
    );
  };

  // Check if context is mostly empty
  const isContextEmpty = !hasContent(targetPosition) &&
                        !hasContent(professionalSummary) &&
                        !hasContent(personalInfo?.fullName) &&
                        !hasExperienceContent(experiences) &&
                        !hasEducationContent(education) &&
                        !hasSkillsContent(skills) &&
                        !hasProjectsContent(projects) &&
                        !hasCertificationsContent(certifications) &&
                        !hasLanguagesContent(languages) &&
                        !hasAwardsContent(awards) &&
                        !hasContent(currentValue);

  if (isContextEmpty) {
    return generateRandomPrompt(fieldKey);
  }

  switch (true) {
    case fieldKey === 'targetPosition':
      return `${baseContext}
Tugas: Buatkan 3 alternatif judul pekerjaan profesional untuk "${currentValue}". Pertimbangkan:
- Variasi standar industri
- Peran serupa dengan judul berbeda
- Judul progres karir
- Kata kunci yang dioptimalkan ATS

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey === 'professionalSummary':
      return `${baseContext}
Tugas: Buatkan 3 variasi ringkasan profesional untuk seseorang yang melamar posisi "${targetPosition}". Ringkasan saat ini: "${currentValue}"

Pertimbangkan:
- 2-3 kalimat yang menyoroti kualifikasi utama
- Kata kunci yang relevan dengan industri
- Bahasa yang fokus pada pencapaian
- Format yang ramah ATS
- Nada profesional

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('experience.') && fieldKey.includes('.jobTitle'):
      return `${baseContext}
Tugas: Buatkan 3 variasi judul pekerjaan profesional untuk "${currentValue}" dalam konteks melamar posisi "${targetPosition}". Pertimbangkan:
- Judul standar industri
- Peran serupa dengan konvensi penamaan berbeda
- Kata kunci yang dioptimalkan ATS
- Penyelarasan progres karir

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('experience.') && fieldKey.includes('.responsibilities'):
      return `${baseContext}
Tugas: Buatkan 3 poin tanggung jawab/pencapaian profesional untuk menggantikan "${currentValue}" untuk seseorang yang melamar posisi "${targetPosition}". Pertimbangkan:
- Dimulai dengan kata kerja aksi
- Pencapaian yang dapat dikuantifikasi bila memungkinkan
- Kata kunci yang relevan dengan industri
- Bahasa yang berfokus pada dampak
- Format yang ramah ATS

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('skills.') && fieldKey.includes('.category'):
      return `${baseContext}
Tugas: Buatkan 3 nama kategori keahlian untuk menggantikan "${currentValue}" untuk seseorang yang melamar posisi "${targetPosition}". Pertimbangkan:
- Pengelompokan keahlian standar industri
- Nama kategori yang ramah ATS
- Terminologi profesional
- Relevan dengan posisi target

Contoh: "Bahasa Pemrograman", "Framework & Library", "Teknologi Database", "Platform Cloud", "Alat Desain", "Manajemen Proyek", "Soft Skills", "Keahlian Teknis"

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('skills.') && fieldKey.includes('.skills'):
      const categoryMatch = fieldKey.match(/skills\.(\d+)\.skills/);
      const categoryIndex = categoryMatch ? parseInt(categoryMatch[1]) : 0;
      const categoryName = context.skills?.categories?.[categoryIndex]?.category || 'Keahlian Umum';
      return `${baseContext}
Tugas: Buatkan 3 daftar keahlian yang dipisahkan koma untuk menggantikan "${currentValue}" untuk kategori keahlian "${categoryName}" untuk seseorang yang melamar posisi "${targetPosition}". Pertimbangkan:
- Keahlian yang secara khusus termasuk dalam kategori "${categoryName}"
- Keahlian yang relevan dengan industri untuk kategori ini
- Kata kunci yang dioptimalkan ATS
- Keahlian yang melengkapi posisi target
- Permintaan pasar saat ini untuk keahlian "${categoryName}"

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet. Setiap saran harus berupa daftar keahlian yang dipisahkan koma dan sesuai kategori "${categoryName}":`;

    case fieldKey.startsWith('projects.') && fieldKey.includes('.description'):
      return `${baseContext}
Tugas: Buatkan 3 variasi deskripsi proyek untuk menggantikan "${currentValue}" untuk seseorang yang melamar posisi "${targetPosition}". Pertimbangkan:
- Gambaran proyek yang jelas dan ringkas
- Highlight teknologi yang digunakan
- Dampak bisnis atau masalah yang dipecahkan
- Nada profesional
- Bahasa yang ramah ATS

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('projects.') && fieldKey.includes('.achievements'):
      return `${baseContext}
Tugas: Buatkan 3 daftar pencapaian proyek yang dipisahkan koma untuk menggantikan "${currentValue}" untuk seseorang yang melamar posisi "${targetPosition}". Pertimbangkan:
- Hasil yang dapat dikuantifikasi bila memungkinkan
- Pencapaian teknis
- Dampak bisnis
- Peningkatan performa
- Peningkatan pengalaman pengguna

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet. Setiap saran harus berupa daftar pencapaian yang dipisahkan koma:`;

    case fieldKey.startsWith('awards.') && fieldKey.includes('.description'):
      return `${baseContext}
Tugas: Buatkan 3 variasi deskripsi penghargaan untuk menggantikan "${currentValue}" untuk seseorang yang melamar posisi "${targetPosition}". Pertimbangkan:
- Konteks pencapaian profesional
- Dampak atau signifikansi penghargaan
- Relevan dengan progres karir
- Ringkas namun bermakna
- Bahasa yang sesuai industri

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    default:
      return `${baseContext}
Tugas: Buatkan 3 peningkatan profesional untuk field "${fieldKey}" dengan nilai saat ini "${currentValue}" untuk seseorang yang melamar posisi "${targetPosition}".
Pertimbangkan bahasa profesional, optimasi ATS, dan standar industri.

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;
  }
}

function generateRandomPrompt(fieldKey: string): string {
  switch (true) {
    case fieldKey === 'targetPosition':
      return `Tugas: Buatkan 3 judul pekerjaan profesional acak. Pertimbangkan:
- Peran pekerjaan populer di berbagai industri
- Posisi entry hingga mid-level
- Kata kunci yang dioptimalkan ATS

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey === 'professionalSummary':
      return `Tugas: Buatkan 3 contoh ringkasan profesional untuk berbagai tingkat karir. Pertimbangkan:
- 2-3 kalimat yang menyoroti kualifikasi umum
- Nada profesional
- Dapat diadaptasi untuk berbagai industri

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('experience.') && fieldKey.includes('.jobTitle'):
      return `Tugas: Buatkan 3 judul pekerjaan profesional umum. Pertimbangkan:
- Peran populer di industri teknologi, bisnis, dan layanan
- Judul yang jelas dan profesional
- Penamaan yang ramah ATS

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('experience.') && fieldKey.includes('.responsibilities'):
      return `Tugas: Buatkan 3 contoh tanggung jawab profesional. Pertimbangkan:
- Dimulai dengan kata kerja aksi
- Pencapaian tempat kerja umum
- Bahasa profesional

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('skills.') && fieldKey.includes('.category'):
      return `Tugas: Buatkan 3 nama kategori keahlian umum. Pertimbangkan:
- Keahlian Teknis, Soft Skills, Bahasa Pemrograman
- Pengelompokan standar industri
- Terminologi profesional

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('skills.') && fieldKey.includes('.skills'):
      return `Tugas: Buatkan 3 daftar keahlian profesional umum yang dipisahkan koma. Pertimbangkan:
- Campuran keahlian teknis dan soft skills
- Keahlian tempat kerja populer
- Kemampuan yang relevan dengan industri

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet. Setiap saran harus berupa daftar yang dipisahkan koma:`;

    case fieldKey.startsWith('projects.') && fieldKey.includes('.description'):
      return `Tugas: Buatkan 3 contoh deskripsi proyek. Pertimbangkan:
- Gambaran proyek yang jelas
- Highlight teknologi
- Nada profesional

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    case fieldKey.startsWith('projects.') && fieldKey.includes('.achievements'):
      return `Tugas: Buatkan 3 contoh pencapaian proyek yang dipisahkan koma. Pertimbangkan:
- Hasil yang dapat dikuantifikasi
- Pencapaian teknis
- Dampak bisnis

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet. Setiap saran harus berupa daftar yang dipisahkan koma:`;

    case fieldKey.startsWith('awards.') && fieldKey.includes('.description'):
      return `Tugas: Buatkan 3 contoh deskripsi penghargaan. Pertimbangkan:
- Konteks pencapaian profesional
- Signifikansi dampak
- Deskripsi yang ringkas

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;

    default:
      return `Tugas: Buatkan 3 saran profesional untuk jenis field ini. Pertimbangkan:
- Bahasa profesional
- Standar industri
- Konten yang jelas dan ringkas

Berikan tepat 3 saran dalam Bahasa Indonesia, satu per baris, tanpa penomoran atau bullet:`;
  }
}

function parseAISuggestions(text: string): string[] {
  const lines = text.split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .filter(line => !line.match(/^[\d\-\*\•]\s*/)) // Remove numbered/bulleted items
    .slice(0, 3); // Take only first 3

  return lines.length > 0 ? lines : ['Tidak ada saran tersedia'];
}
