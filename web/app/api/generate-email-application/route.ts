export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createApiHandler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { createClient } from '@/lib/supabase-server';
import { generateAIEmailApplication } from '@/utils/ai-generators/emailApplicationGenerator';
import { trackApiUsage } from '@/lib/mixpanel-server';

async function handleRequest(request: NextRequest) {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;

  let formData = new FormData();
  try {
    formData = await request.formData();

    const jobDescription = formData.get('jobDescription') as string | null;
    const jobImage = formData.get('jobImage') as File | null;
    const unauthenticatedResumeFile = formData.get('unauthenticatedResumeFile') as File | null;
    const unauthenticatedResumeFileName = formData.get('unauthenticatedResumeFileName') as string | null;

    if (!jobDescription && !jobImage) {
      return NextResponse.json({
        error: 'Deskripsi pekerjaan atau gambar lowongan diperlukan'
      }, { status: 400 });
    }

    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    let buffer: ArrayBuffer;
    let fileName: string;

    if (unauthenticatedResumeFile && unauthenticatedResumeFileName) {
      buffer = await unauthenticatedResumeFile.arrayBuffer();
      fileName = unauthenticatedResumeFileName.toLowerCase();
    } else if (accessToken) {
      // Create Supabase client
      const supabase = await createClient();

      // Get user with the provided token
      const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

      if (userError || !user) {
        console.error('Error getting user with token:', userError);
        return NextResponse.json({
          error: 'Anda harus login untuk menggunakan fitur ini'
        }, { status: 401 });
      }
      userId = user.id;

      // Get resume data and token balance from profile
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('resume_file_name, tokens')
        .eq('id', userId)
        .single();

      if (fetchError || !profile) {
        return NextResponse.json({
          error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.'
        }, { status: 404 });
      }

      // Check token balance - email application costs 5 tokens
      const requiredTokens = 5;
      const currentTokens = (profile.tokens as number | null) ?? 0;

      if (currentTokens < requiredTokens) {
        return NextResponse.json({
          error: 'Token Anda tidak cukup untuk menggunakan fitur ini. Silakan top up token Anda.'
        }, { status: 402 });
      }

      // Get the resume file from storage
      const { data: resumeFile, error: storageError } = await supabase.storage
        .from('resumes')
        .download(profile.resume_file_name);

      if (storageError || !resumeFile) {
        captureApiError('generate-email-application', storageError || 'Resume file access error', {
          userId,
          type: 'resume_storage_access',
          resumeFileName: profile.resume_file_name
        });
        return NextResponse.json({ 
          error: 'Tidak dapat mengakses file resume. Harap unggah ulang resume Anda.' 
        }, { status: 500 });
      }

      // Convert the file to buffer and determine correct mime type
      buffer = await resumeFile.arrayBuffer();
      fileName = profile.resume_file_name.toLowerCase();
    } else {
      return NextResponse.json({ 
        error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.' 
      }, { status: 404 });
    }
    
    // Get the correct mime type based on file extension
    let mimeType: string;
    if (fileName.endsWith('.pdf')) {
      mimeType = 'application/pdf';
    } else if (fileName.endsWith('.docx')) {
      mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (fileName.endsWith('.png')) {
      mimeType = 'image/png';
    } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
      mimeType = 'image/jpeg';
    } else {
      mimeType = 'text/plain';
    }
    
    // Prepare job image if it exists
    let jobImageData: { buffer: ArrayBuffer, mimeType: string } | undefined;
    if (jobImage) {
      const jobImageBuffer = await jobImage.arrayBuffer();
      jobImageData = { 
        buffer: jobImageBuffer, 
        mimeType: jobImage.type 
      };
    }

    // Generate email application using AI with the file
    const emailApplication = await generateAIEmailApplication(
      { buffer, mimeType },
      jobDescription || undefined,
      jobImageData
    );

    // Save the email to Supabase and deduct tokens (only for authenticated users)
    let emailId: string | undefined;
    if (userId && accessToken) {
      try {
        const supabase = await createClient();

        // Save the email to the database
        const { data: emailData, error: saveError } = await supabase
          .from('emails')
          .insert({
            user_id: userId,
            subject: emailApplication.subject,
            body: emailApplication.body
          })
          .select('id')
          .single();

        if (saveError) {
          console.error('Error saving email to database:', saveError);
        } else {
          emailId = emailData?.id;
          console.log(`Successfully saved email ${emailId} for user ${userId}`);
        }

        // Get current token balance
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('tokens')
          .eq('id', userId)
          .single();

        if (profileError) {
          console.error('Error fetching user tokens for deduction:', profileError);
        } else if (profileData) {
          const currentTokens = profileData.tokens ?? 0;
          const requiredTokens = 5;
          const newTokens = Math.max(currentTokens - requiredTokens, 0);

          const { error: updateError } = await supabase
            .from('profiles')
            .update({ tokens: newTokens })
            .eq('id', userId);

          if (updateError) {
            console.error('Error deducting user tokens:', updateError);
          } else {
            console.log(`Successfully deducted ${requiredTokens} tokens from user ${userId}. New balance: ${newTokens}`);
          }
        }
      } catch (tokenError) {
        console.error('Unexpected error while deducting tokens:', tokenError);
      }
    }

    // Calculate request duration
    const duration = Date.now() - startTime;

    // Track successful API usage in Mixpanel
    trackApiUsage(
      'generate-email-application',
      'success',
      duration,
      {
        has_job_description: !!jobDescription,
        has_job_image: !!jobImage,
        is_authenticated: !!userId,
        resume_type: fileName.split('.').pop(),
        tokens_deducted: userId ? 5 : 0
      },
      userId
    );

    return NextResponse.json({
      success: true,
      emailApplication,
      emailId: emailId
    });
    
  } catch (error) {
    console.error('Error generating email application:', error);
    
    // Calculate request duration even for errors
    const duration = Date.now() - startTime;
    
    // Track failed API usage in Mixpanel
    trackApiUsage(
      'generate-email-application', 
      'error',
      duration,
      {
        error_message: error instanceof Error ? error.message : 'Unknown error',
        is_authenticated: !!userId
      },
      userId
    );
    
    // Capture error details with Rollbar
    captureApiError('generate-email-application', error, {
      request_url: request.url
    });
    
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan saat membuat email lamaran'
    }, { status: 500 });
  }
}

// Export the handler with error reporting wrapper
export const POST = createApiHandler('generate-email-application', handleRequest);
