export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { preventCaching } from '@/utils/cacheControl';
import { v4 as uuidv4 } from 'uuid';
import { createClient } from '@/lib/supabase-server';
import rollbar from '@/lib/rollbar';
import { trackApiUsage, trackEvent } from '@/lib/mixpanel-server';

// Simulasi ekstraksi data dari resume
// Definisikan type ResumeData untuk mencegah error TypeScript
type ResumeData = {
  id: string;
  name: string;
  email: string;
  phone: string;
  skills: string[];
  education: Array<{
    institution: string;
    degree: string;
    year: string;
  }>;
  experience: Array<{
    company: string;
    position: string;
    duration: string;
    description: string;
  }>;
  fileName: string;
  uploadedAt: string;
  fileUrl?: string; // Tambahkan fileUrl sebagai properti opsional
};

// Placeholder untuk implementasi ekstraksi resume menggunakan AI
// TODO: Implementasikan ekstraksi resume dengan AI

export async function POST(request: NextRequest) {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;
  
  try {
    const formData = await request.formData();
    const file = formData.get('resume') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'Tidak ada file yang diunggah' }, { status: 400 });
    }
    
    // Validasi tipe file
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/png', 'image/jpeg', 'image/jpg'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'Format file tidak didukung. Harap unggah file PDF, DOCX, PNG, JPG, atau JPEG' }, { status: 400 });
    }
    
    // Validasi ukuran file (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json({ error: 'Ukuran file terlalu besar. Maksimal 5MB.' }, { status: 400 });
    }
    
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');
    
    if (!accessToken) {
      return NextResponse.json({ error: 'Missing authentication token' }, { status: 401 });
    }
    
    // Create Supabase client
    const supabase = await createClient();
    
    // Get user with the provided token
    const { data: { user }, error: getUserError } = await supabase.auth.getUser(accessToken);
    
    if (getUserError || !user) {
      console.error('Error getting user with token:', getUserError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    userId = user.id;

    // Mendapatkan buffer file
    const bytes = await file.arrayBuffer();
    
    // Get file extension
    const fileExtension = file.name.split('.').pop();
    const fileName = `${user.id}.${fileExtension}`;

    let url: string | null = null;
    
    try {
      // Langsung upload file ke bucket 'resumes'
      const { data, error } = await supabase
        .storage
        .from('resumes')
        .upload(fileName, bytes, {
          cacheControl: '3600',
          upsert: true // Replace if file exists
        });
      
      if (error) {
        // Jika error karena bucket tidak ditemukan, tampilkan pesan user-friendly
        if (error.message?.toLowerCase().includes('bucket not found')) {
          console.error('Bucket "resumes" tidak ditemukan');
          rollbar.logError('Bucket "resumes" tidak ditemukan', {
            endpoint: 'upload-resume/POST',
            context: 'Storage bucket not found',
            userId: user?.id
          });
          return NextResponse.json({
            error: "Kesalahan dalam mengunggah resume."
          }, { status: 500 });
        }
        // Error lain
        console.error('Supabase storage error:', error);
        rollbar.logError(error, {
          endpoint: 'upload-resume/POST',
          context: 'Supabase storage error during file upload',
          userId: user?.id,
          errorDetails: JSON.stringify(error)
        });
        return NextResponse.json({ error: 'Gagal mengunggah file' }, { status: 500 });
      }
      
      // Get public URL for the file
      const { data: { publicUrl } } = supabase
        .storage
        .from('resumes')
        .getPublicUrl(fileName);
      
      // Include the public URL in the response
      url = publicUrl;
      
      // Coba simpan ke tabel profiles jika ada
      try {
        const { error: updateError } = await supabase
          .from('profiles')
          .upsert({
            id: user.id,
            resume_file_name: fileName,
            real_file_name: file.name,
            resume_url: publicUrl,
            resume_uploaded_at: new Date().toISOString(),
          }, {
            onConflict: 'id'
          });
          
        if (updateError) {
          console.error('Profile update error:', updateError);
          rollbar.logError(updateError, {
            endpoint: 'upload-resume/POST',
            context: 'Failed to update profile with resume data',
            userId: user?.id,
            errorDetails: JSON.stringify(updateError)
          });
          return NextResponse.json({ error: 'Gagal menyimpan data resume ke database' }, { status: 500 });
        }
      } catch (profileError) {
        console.error('Error updating profile:', profileError);
        rollbar.logError(profileError instanceof Error ? profileError : 'Profile update error', {
          endpoint: 'upload-resume/POST',
          context: 'Exception during profile update',
          userId: user?.id,
          errorDetails: JSON.stringify(profileError, Object.getOwnPropertyNames(profileError))
        });
        return NextResponse.json({ error: 'Terjadi kesalahan saat menyimpan data resume' }, { status: 500 });
      }
    } catch (storageError) {
      console.error('Storage operation error:', storageError);
      rollbar.logError(storageError instanceof Error ? storageError : 'Storage operation error', {
        endpoint: 'upload-resume/POST',
        context: 'Exception during storage operation',
        userId: user?.id,
        errorDetails: JSON.stringify(storageError, Object.getOwnPropertyNames(storageError))
      });
      return NextResponse.json({ error: 'Terjadi kesalahan pada storage' }, { status: 500 });
    }
    
    // Calculate request duration
    const duration = Date.now() - startTime;
    
    // Track successful resume upload in Mixpanel
    trackApiUsage(
      'upload-resume', 
      'success',
      duration,
      {
        file_type: file.type,
        file_size_bytes: file.size,
        user_id: userId
      },
      userId
    );
    
    // Also track as a specific event type for easier reporting
    trackEvent(
      'Resume Uploaded',
      {
        file_type: file.type,
        file_size_bytes: file.size
      },
      userId
    );
    
    return NextResponse.json({ 
      success: true,
      message: 'Resume berhasil diunggah',
      data: {
        fileName: file.name,
        publicUrl: url,
        uploadedAt: new Date().toISOString(),
      }
    });
    
  } catch (error) {
    // Calculate request duration even for errors
    const duration = Date.now() - startTime;
    
    // Track failed API usage in Mixpanel
    trackApiUsage(
      'upload-resume', 
      'error',
      duration,
      {
        error_message: error instanceof Error ? error.message : 'Unknown error',
        is_authenticated: !!userId
      },
      userId
    );
    console.error('Error uploading resume:', error);
    rollbar.logError(error instanceof Error ? error : 'Error uploading resume', {
      endpoint: 'upload-resume/POST',
      context: 'Top-level exception in POST handler',
      errorDetails: JSON.stringify(error, Object.getOwnPropertyNames(error))
    });
    return NextResponse.json({ error: 'Terjadi kesalahan saat mengunggah resume' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;
  
  try {
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');
    
    if (!accessToken) {
      return NextResponse.json({ error: 'Missing authentication token' }, { status: 401 });
    }
    
    // Create Supabase client
    const supabase = await createClient();
    
    // Get user with the provided token
    const { data: { user }, error: getUserError } = await supabase.auth.getUser(accessToken);
    
    if (getUserError || !user) {
      console.error('Error getting user with token:', getUserError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    userId = user.id;

    try {
      // Get current resume filename from profiles
      const { data: profile } = await supabase
        .from('profiles')
        .select('resume_file_name')
        .eq('id', user.id)
        .single();
      
      if (!profile || !profile.resume_file_name) {
        return NextResponse.json({ error: 'Tidak ada resume yang ditemukan' }, { status: 404 });
      }
      
      // Delete file from storage
      const { error: deleteError } = await supabase
        .storage
        .from('resumes')
        .remove([profile.resume_file_name]);
        
      if (deleteError) {
        console.error('Error deleting resume file:', deleteError);
        rollbar.logError(deleteError, {
          endpoint: 'upload-resume/DELETE',
          context: 'Failed to delete resume file from storage',
          userId: user?.id,
          errorDetails: JSON.stringify(deleteError)
        });
        return NextResponse.json({ error: 'Gagal menghapus file resume' }, { status: 500 });
      }
      
      // Update profiles table to remove resume information
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          resume_file_name: null,
          real_file_name: null,
          resume_url: null,
          resume_uploaded_at: null
        })
        .eq('id', user.id);
        
      if (updateError) {
        console.error('Error updating profile:', updateError);
        rollbar.logError(updateError, {
          endpoint: 'upload-resume/DELETE',
          context: 'Failed to update profile after deleting resume',
          userId: user?.id,
          errorDetails: JSON.stringify(updateError)
        });
        return NextResponse.json({ error: 'Gagal mengupdate profil' }, { status: 500 });
      }
      
      // Calculate request duration
      const duration = Date.now() - startTime;
      
      // Track successful resume deletion in Mixpanel
      trackApiUsage(
        'delete-resume', 
        'success',
        duration,
        {
          user_id: userId
        },
        userId
      );
      
      // Also track as a specific event type for easier reporting
      trackEvent(
        'Resume Deleted',
        {
          success: true
        },
        userId
      );
      
      return NextResponse.json({ success: true, message: 'Resume berhasil dihapus' });
      
    } catch (error) {
      // Calculate request duration even for errors
      const duration = Date.now() - startTime;
      
      // Track failed API usage in Mixpanel
      trackApiUsage(
        'delete-resume', 
        'error',
        duration,
        {
          error_message: error instanceof Error ? error.message : 'Unknown error',
          is_authenticated: !!userId
        },
        userId
      );
      console.error('Error deleting resume:', error);
      rollbar.logError(error instanceof Error ? error : 'Error deleting resume', {
        endpoint: 'upload-resume/DELETE',
        context: 'Top-level exception in DELETE handler',
        errorDetails: JSON.stringify(error, Object.getOwnPropertyNames(error))
      });
      return NextResponse.json({ error: 'Terjadi kesalahan saat menghapus resume' }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Error in DELETE handler:', error);
    rollbar.logError(error instanceof Error ? error : 'Error in DELETE handler', {
      endpoint: 'upload-resume/DELETE',
      context: 'Top-level exception in DELETE handler',
      errorDetails: JSON.stringify(error, Object.getOwnPropertyNames(error))
    });
    return NextResponse.json({ error: 'Terjadi kesalahan internal' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;
  
  try {
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');
    
    if (!accessToken) {
      return preventCaching(NextResponse.json({ error: 'Missing authentication token' }, { status: 401 }));
    }
    
    // Create Supabase client
    const supabase = await createClient();
    
    // Get user with the provided token
    const { data: { user }, error: getUserError } = await supabase.auth.getUser(accessToken);

    
    if (user) {
      userId = user.id;
    }
    
    if (getUserError || !user) {
      console.error('Error getting user with token:', getUserError);
      return preventCaching(NextResponse.json({ error: 'Unauthorized' }, { status: 401 }));
    }

    try {
      // Get user's resume info from profiles table
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('resume_file_name, real_file_name, resume_url, resume_uploaded_at')
        .eq('id', user.id)
        .single();
      
      if (!error && profile && profile.resume_file_name) {
        // Calculate request duration for successful resume fetch
        const duration = Date.now() - startTime;
        
        // Track successful resume fetch in Mixpanel
        trackApiUsage(
          'get-resume', 
          'success',
          duration,
          {
            source: 'profiles',
          },
          userId
        );
        
        return preventCaching(NextResponse.json({
          hasResume: true,
          fileName: profile.real_file_name,
          publicUrl: profile.resume_url,
          uploadedAt: profile.resume_uploaded_at,
        }));
      }
      
      // Jika tabel profiles tidak ada atau error, coba cari file di storage berdasarkan user ID
      try {
        // Cari file yang nama file-nya mengandung user ID
        const { data: files, error: listError } = await supabase
          .storage
          .from('resumes')
          .list();
          
        if (listError || !files) {
          if (listError) {
            console.error('List error:', listError);
            rollbar.logError(listError, {
              endpoint: 'upload-resume/GET',
              context: 'Error listing files in resumes bucket',
              userId: user?.id,
              errorDetails: JSON.stringify(listError)
            });
          } else if (!files) {
            console.error('Files not found');
          }
          return preventCaching(NextResponse.json({ hasResume: false }));
        }
        
        // Filter file berdasarkan ID user
        const userFiles = files.filter(file => file.name.includes(`${user.id}`));
        
        if (userFiles.length === 0) {
          return preventCaching(NextResponse.json({ hasResume: false }));
        }

        const userFile = userFiles[0];
        
        const { data: { publicUrl } } = supabase
          .storage
          .from('resumes')
          .getPublicUrl(userFile.name);
        
        // Calculate request duration for successful resume fetch from storage
        const duration = Date.now() - startTime;
        
        // Track successful resume fetch in Mixpanel
        trackApiUsage(
          'get-resume', 
          'success',
          duration,
          {
            source: 'storage',
            has_resume: true
          },
          userId
        );
        
        return preventCaching(NextResponse.json({
          hasResume: true,
          fileName: userFile.name,
          publicUrl: publicUrl,
          uploadedAt: userFile.created_at || new Date().toISOString(),
        }));
      } catch (storageError) {
        console.error('Storage error:', storageError);
        rollbar.logError(storageError instanceof Error ? storageError : 'Storage error in GET resume', {
          endpoint: 'upload-resume/GET',
          context: 'Failed to fetch resume from storage',
          userId: user?.id
        });
        return preventCaching(NextResponse.json({ hasResume: false }));
      }
    } catch (profileError) {
      console.error('Profile error:', profileError);
      rollbar.logError(profileError instanceof Error ? profileError : 'Profile error in GET resume', {
        endpoint: 'upload-resume/GET',
        context: 'Failed to fetch profile data',
        userId: user?.id
      });
      const response = NextResponse.json({ hasResume: false });
      response.headers.set("Netlify-Vary", "query");
      return response;
    }
    
  } catch (error) {
    // Calculate request duration even for errors
    const duration = Date.now() - startTime;
    
    // Track failed API usage in Mixpanel
    trackApiUsage(
      'get-resume', 
      'error',
      duration,
      {
        error_message: error instanceof Error ? error.message : 'Unknown error',
        is_authenticated: !!userId
      },
      userId
    );
    
    console.error('Resume fetch error:', error);
    rollbar.logError(error instanceof Error ? error : 'Resume fetch error in GET resume', {
      endpoint: 'upload-resume/GET',
      context: 'Top-level error in GET handler',
      errorDetails: JSON.stringify(error, Object.getOwnPropertyNames(error))
    });
    return preventCaching(NextResponse.json({ error: 'Terjadi kesalahan saat mengambil data resume' }, { status: 500 }));
  }
}
