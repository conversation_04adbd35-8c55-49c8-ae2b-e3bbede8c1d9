import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ purchaseId: string }> }
) {
  try {
    const supabase = await createClient();
    
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');
    
    if (!accessToken) {
      return NextResponse.json(
        { error: 'Missing authentication token' },
        { status: 401 }
      );
    }

    // Validate the token and get user details
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      console.error('Authentication error with token:', userError);
      return NextResponse.json(
        { error: 'Unauthorized: Invalid or expired token.' },
        { status: 401 }
      );
    }

    const { purchaseId } = await params;

    if (!purchaseId) {
      return NextResponse.json(
        { error: 'Purchase ID is required' },
        { status: 400 }
      );
    }

    // Query the specific purchase for the current user
    const { data: purchase, error: purchaseError } = await supabase
      .from('purchases')
      .select('*')
      .eq('id', purchaseId)
      .eq('user_id', user.id)
      .single();

    if (purchaseError) {
      if (purchaseError.code === 'PGRST116') {
        // No rows returned
        return NextResponse.json(
          { error: 'Purchase not found' },
          { status: 404 }
        );
      }
      console.error('Error fetching purchase:', purchaseError);
      return NextResponse.json(
        { error: 'Failed to fetch purchase' },
        { status: 500 }
      );
    }

    // Return the purchase status and relevant information
    return NextResponse.json({
      success: true,
      status: purchase.status,
      purchase_id: purchase.id,
      token_amount: purchase.token_amount,
      token_package: purchase.token_package,
      amount: purchase.amount,
      currency: purchase.currency,
      payment_method: purchase.payment_method,
      created_at: purchase.created_at,
      payment_completed_at: purchase.payment_completed_at,
    });

  } catch (error) {
    console.error('Purchase status API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}