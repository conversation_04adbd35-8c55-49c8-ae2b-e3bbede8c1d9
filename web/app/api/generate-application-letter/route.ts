export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { generateAIApplicationLetter } from '@/utils/ai-generators/applicationLetterGenerator';
import { generatePlainTextLetter, prepareStreamingData } from '@/utils/ai-generators/applicationLetterGeneratorStreaming';
import { createApiHandler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { trackApiUsage } from '@/lib/mixpanel-server';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';

async function handleRequest(request: NextRequest) {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;
  
  
  let formData = new FormData();
  try {
    formData = await request.formData();
    
    const jobDescription = formData.get('jobDescription') as string | null;
    const jobImage = formData.get('jobImage') as File | null;
    const unauthenticatedResumeFile = formData.get('unauthenticatedResumeFile') as File | null;
    const unauthenticatedResumeFileName = formData.get('unauthenticatedResumeFileName') as string | null;
    const templateId = formData.get('templateId') as string | 'plain-text';
    const editedLetterText = formData.get('editedLetterText') as string | null;
    const existingLetterId = formData.get('existingLetterId') as string | null;
    
    if (!jobDescription && !jobImage) {
      return NextResponse.json({
        error: 'Deskripsi pekerjaan dan gambar lowongan diperlukan' 
      }, { status: 400 });
    }
    
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    let buffer: ArrayBuffer;
    let fileName: string;

    if (unauthenticatedResumeFile && unauthenticatedResumeFileName) {
      buffer = await unauthenticatedResumeFile.arrayBuffer();
      fileName = unauthenticatedResumeFileName.toLowerCase();
    } else if (accessToken) {
      // Create Supabase client
      const supabase = await createClient();
          
      // Get user with the provided token
      const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

      if (userError || !user) {
        console.error('Error getting user with token:', userError);
        return NextResponse.json({ 
          error: 'Anda harus login untuk menggunakan fitur ini' 
        }, { status: 401 });
      }
      userId = user.id;

      // Get resume data from Supabase storage
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('resume_file_name, tokens')
        .eq('id', userId)
        .single();

      if (fetchError || !profile) {
        return NextResponse.json({ 
          error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.' 
        }, { status: 404 });
      }

      // Check token balance against template cost (skip for edit operations)
      const selectedTemplate = getTemplateById(templateId);
      const requiredTokens = selectedTemplate?.tokenCost ?? 0;
      const currentTokens = (profile.tokens as number | null) ?? 0;

      // Only check token balance for new generations, not for edits
      if (!editedLetterText && requiredTokens > 0 && currentTokens < requiredTokens) {
        return NextResponse.json({
          error: 'Token Anda tidak cukup untuk menggunakan template ini. Silakan top up token Anda.'
        }, { status: 402 });
      }

      // Get the resume file from storage
      const { data: resumeFile, error: storageError } = await supabase.storage
        .from('resumes')
        .download(profile.resume_file_name);

      if (storageError || !resumeFile) {
        captureApiError('generate-application-letter', storageError || 'Resume file access error', {
          userId,
          type: 'resume_storage_access',
          resumeFileName: profile.resume_file_name
        });
        return NextResponse.json({ 
          error: 'Tidak dapat mengakses file resume. Harap unggah ulang resume Anda.' 
        }, { status: 500 });
      }

      // Convert the file to buffer and determine correct mime type
      buffer = await resumeFile.arrayBuffer();
      fileName = profile.resume_file_name.toLowerCase();
    } else {
      return NextResponse.json({ 
        error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.' 
      }, { status: 404 });
    }
    
    // Get the correct mime type based on file extension
    let mimeType: string;
    if (fileName.endsWith('.pdf')) {
      mimeType = 'application/pdf';
    } else if (fileName.endsWith('.docx')) {
      mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (fileName.endsWith('.png')) {
      mimeType = 'image/png';
    } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
      mimeType = 'image/jpeg';
    } else {
      mimeType = 'text/plain';
    }
    
    // Prepare job image if it exists
    let jobImageData: { buffer: ArrayBuffer, mimeType: string } | undefined;
    if (jobImage) {
      const jobImageBuffer = await jobImage.arrayBuffer();
      jobImageData = { 
        buffer: jobImageBuffer, 
        mimeType: jobImage.type 
      };
    }

    // Check if streaming is requested - should be used for ALL templates since they all use OpenAI
    const useStreaming = formData.get('useStreaming') === 'true';
    const useUnifiedStreaming = formData.get('useUnifiedStreaming') === 'true';
    
    if (useUnifiedStreaming) {
      // Prepare data for unified streaming (both plain text and template generation)
      
      // Convert resume file to appropriate format for edge function
      let resumeData: string;
      let resumeMimeType: string = mimeType;
      
      if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        // Extract text from DOCX for edge function
        const { extractTextFromDocx } = await import('@/utils/docxUtils');
        resumeData = await extractTextFromDocx(buffer);
        resumeMimeType = 'text/plain';
      } else if (mimeType === 'text/plain') {
        resumeData = Buffer.from(buffer).toString('utf-8');
      } else {
        // For PDF and images, convert to base64
        resumeData = Buffer.from(buffer).toString('base64');
      }
      
      // Convert job image to base64 if provided
      let jobImageBase64: string | undefined;
      if (jobImageData) {
        jobImageBase64 = Buffer.from(jobImageData.buffer).toString('base64');
      }
      
      // Get template HTML if template is selected
      const selectedTemplate = getTemplateById(templateId);
      const templateHtml = selectedTemplate?.templateHtml;
      
      // Calculate request duration for preparation
      const duration = Date.now() - startTime;
      
      // Track successful API usage in Mixpanel
      trackApiUsage(
        'generate-application-letter-unified-streaming',
        'success',
        duration,
        {
          has_job_description: !!jobDescription,
          has_job_image: !!jobImage,
          is_authenticated: !!userId,
          resume_type: fileName.split('.').pop(),
          template_id: templateId,
          phase: 'preparation'
        },
        userId
      );
      
      // Return unified streaming information for client
      return NextResponse.json({
        success: true,
        data: {
          streaming: {
            enabled: true,
            unified: true,
            endpoint: '/api/edge/generate-letter-unified',
            data: {
              resumeData,
              resumeMimeType,
              jobDescription: jobDescription || undefined,
              jobImage: jobImageBase64,
              templateId,
              templateHtml,
              userId,
              letterId: existingLetterId || undefined,
              isEdit: !!editedLetterText
            }
          },
          price: selectedTemplate?.tokenCost ? 15000 : undefined,
          currency: selectedTemplate?.tokenCost ? 'IDR' : undefined
        }
      });
    } else if (useStreaming) {
      // Legacy streaming approach (kept for backward compatibility)
      // Phase 1: Generate plain text letter using Gemini (fast)
      const plainText = await generatePlainTextLetter(
        { buffer, mimeType },
        jobDescription || undefined,
        jobImageData,
        editedLetterText || undefined
      );
      
      // Phase 2: Prepare streaming data
      const streamingData = await prepareStreamingData(
        plainText,
        templateId,
        userId,
        !!editedLetterText, // isEdit = true if editedLetterText is provided
        existingLetterId || undefined // Pass existing letterId for updates
      );
      
      // Calculate request duration for phase 1
      const duration = Date.now() - startTime;
      
      // Track successful API usage in Mixpanel
      trackApiUsage(
        'generate-application-letter-streaming',
        'success',
        duration,
        {
          has_job_description: !!jobDescription,
          has_job_image: !!jobImage,
          is_authenticated: !!userId,
          resume_type: fileName.split('.').pop(),
          template_id: templateId,
          phase: 'plain-text'
        },
        userId
      );
      
      // Return streaming information for client to continue with Edge Function
      return NextResponse.json({
        success: true,
        data: {
          plainText: streamingData.plainText,
          letterId: streamingData.letterId,
          price: streamingData.price,
          currency: streamingData.currency,
          streaming: {
            enabled: true,
            endpoint: '/api/edge/generate-application-letter',
            templateHtml: streamingData.templateHtml,
            templateId: streamingData.templateId,
            isEdit: streamingData.isEdit
          }
        }
      });
    } else {
      // Use the original non-streaming approach for backward compatibility
      const applicationLetterData = await generateAIApplicationLetter(
        { buffer, mimeType },
        userId,
        jobDescription || undefined,
        jobImageData,
        templateId,
        editedLetterText || undefined
      );
      
      // Calculate request duration
      const duration = Date.now() - startTime;
      
      // Track successful API usage in Mixpanel
      trackApiUsage(
        'generate-application-letter',
        'success',
        duration,
        {
          has_job_description: !!jobDescription,
          has_job_image: !!jobImage,
          is_authenticated: !!userId,
          resume_type: fileName.split('.').pop(),
          has_designs: !!applicationLetterData.design
        },
        userId
      );
      
      return NextResponse.json({
        success: true,
        data: applicationLetterData
      });
    }
    
  } catch (error) {
    console.error('Error generating application letter:', error);
    
    // Calculate request duration even for errors
    const duration = Date.now() - startTime;
    
    // Track failed API usage in Mixpanel
    trackApiUsage(
      'generate-application-letter', 
      'error',
      duration,
      {
        error_message: error instanceof Error ? error.message : 'Unknown error',
        is_authenticated: !!userId
      },
      userId
    );
    
    // Capture error details with Rollbar
    captureApiError('generate-application-letter', error, {
      requestUrl: request.url
    });
    
    return NextResponse.json({
      success: false,
      error: 'Gagal membuat surat lamaran. Silakan coba lagi.'
    }, { status: 500 });
  }
}

// Export the handler with error reporting wrapper
export const POST = createApiHandler('generate-application-letter', handleRequest);
