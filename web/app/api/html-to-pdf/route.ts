export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { captureApiError } from '@/utils/errorMonitoring';

export async function POST(request: NextRequest) {
  try {
    // Get HTML content from request body
    const { html, fileName = 'document' } = await request.json();

    if (!html) {
      return NextResponse.json(
        { error: 'HTML content is required' },
        { status: 400 }
      );
    }

    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    if (!accessToken) {
      return NextResponse.json({
        error: 'Authentication required'
      }, { status: 401 });
    }

    // Create Supabase client
    const supabase = await createClient();

    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return NextResponse.json({
        error: 'Invalid authentication token'
      }, { status: 401 });
    }

    // Call the Supabase Edge Function to generate PDF
    try {
      const { data: pdfData, error: edgeFunctionError } = await supabase.functions.invoke('html-to-pdf', {
        body: {
          html: html,
          fileName: fileName,
        },
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (edgeFunctionError) {
        console.error('Edge Function error:', edgeFunctionError);
        captureApiError('edge-function-html-to-pdf', edgeFunctionError, {
          fileName: fileName
        });
        return NextResponse.json({
          error: 'Failed to generate PDF. Please try again.'
        }, { status: 500 });
      }

      // Return PDF as response
      return new NextResponse(pdfData, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${fileName}.pdf"`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      });
    } catch (pdfError) {
      console.error('PDF generation error:', pdfError);
      captureApiError('pdf-generation', pdfError as Error, {
        fileName: fileName
      });
      return NextResponse.json({
        error: 'Failed to generate PDF. Please try again.'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error generating PDF:', error);

    // Capture error details with Rollbar
    captureApiError('html-to-pdf', error, {
      requestUrl: request.url
    });

    return NextResponse.json({
      success: false,
      error: 'Failed to generate PDF. Please try again.'
    }, { status: 500 });
  }
}