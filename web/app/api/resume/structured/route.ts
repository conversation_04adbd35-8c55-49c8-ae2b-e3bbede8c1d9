import { NextRequest, NextResponse } from 'next/server';
import { StructuredResumeData } from '@/types/resume-structured';

// In-memory storage for demo purposes
// In production, this should be stored in a database
const structuredDataStore = new Map<string, StructuredResumeData>();

export async function POST(request: NextRequest) {
  try {
    const { userId, data }: { userId: string; data: StructuredResumeData } = await request.json();
    
    if (!userId || !data) {
      return NextResponse.json(
        { error: 'Missing userId or data' },
        { status: 400 }
      );
    }
    
    // Store the structured data
    structuredDataStore.set(userId, data);
    
    return NextResponse.json(
      { 
        success: true, 
        message: 'Structured data saved successfully',
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error saving structured data:', error);
    return NextResponse.json(
      { error: 'Failed to save structured data' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }
    
    const data = structuredDataStore.get(userId);
    
    if (!data) {
      return NextResponse.json(
        { error: 'No structured data found for this user' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { 
        success: true, 
        data,
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error retrieving structured data:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve structured data' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }
    
    const existed = structuredDataStore.has(userId);
    structuredDataStore.delete(userId);
    
    return NextResponse.json(
      { 
        success: true, 
        message: existed ? 'Structured data deleted successfully' : 'No data found to delete',
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting structured data:', error);
    return NextResponse.json(
      { error: 'Failed to delete structured data' },
      { status: 500 }
    );
  }
}