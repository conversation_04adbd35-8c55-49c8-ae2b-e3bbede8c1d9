import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase-server";
import { createApiHandler } from "@/utils/apiErrorHandler";

async function handleGet(_req: NextRequest, { params }: { params: { id: string } }) {
  const supabase = await createClient();
  const { id } = await params;
  const { data, error } = await supabase.from("resumes").select("*").eq("id", id).single();
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 404 });
  }
  return NextResponse.json(data);
}

async function handlePut(req: NextRequest, { params }: { params: { id: string } }) {
  const supabase = await createClient();
  const body = (await req.json()) as { data: any };

  // Regenerate HTML via OpenAI
  const { generateResumeHtml } = await import("@/utils/resumeHtml");
  const html = await generateResumeHtml(body.data);
  const { id } = await params;
  const { error } = await supabase
    .from("resumes")
    .update({ data: body.data, html, pdf_url: null, status: "done" })
    .eq("id", id);

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  return NextResponse.json({ ok: true });
}

export const GET = createApiHandler("resume-get", handleGet);
export const PUT = createApiHandler("resume-update", handlePut);
