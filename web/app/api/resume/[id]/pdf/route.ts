import { NextRequest, NextResponse } from "next/server";
import { create<PERSON><PERSON><PERSON>and<PERSON> } from "@/utils/apiErrorHandler";

// Access the in-memory stores
function getPDFStore(): Record<string, string> {
  const g = globalThis as unknown as { __resumePDFStore?: Record<string, string> };
  if (!g.__resumePDFStore) {
    g.__resumePDFStore = {};
  }
  return g.__resumePDFStore;
}

function getGenerationStore(): Record<string, any> {
  const g = globalThis as unknown as { __resumeGenStore?: Record<string, any> };
  if (!g.__resumeGenStore) {
    g.__resumeGenStore = {};
  }
  return g.__resumeGenStore;
}

async function handleGet(_req: NextRequest, { params }: { params: { id: string } }) {
  const { id } = params;
  
  try {
    // Get the generation record
    const genStore = getGenerationStore();
    const generationRecord = genStore[id];
    
    if (!generationRecord) {
      return NextResponse.json({ error: "Resume generation not found" }, { status: 404 });
    }
    
    if (generationRecord.status !== "done") {
      return NextResponse.json({ 
        error: "Resume generation not completed yet",
        status: generationRecord.status 
      }, { status: 422 });
    }
    
    // Get the PDF data from store
    const pdfStore = getPDFStore();
    const pdfBase64 = pdfStore[id];
    
    if (!pdfBase64) {
      return NextResponse.json({ error: "PDF data not found" }, { status: 404 });
    }
    
    // Convert base64 to buffer
    const pdfBuffer = Buffer.from(pdfBase64, 'base64');
    
    // Return the PDF with appropriate headers
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="resume-${id}.pdf"`,
        'Content-Length': pdfBuffer.length.toString()
      }
    });
    
  } catch (error) {
    console.error('Error serving PDF:', error);
    return NextResponse.json({ error: "Failed to serve PDF" }, { status: 500 });
  }
}

export const GET = createApiHandler("resume-pdf", handleGet);
