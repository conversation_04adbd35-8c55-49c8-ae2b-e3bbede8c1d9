import { NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";
import { createClient } from "@/lib/supabase-server";
import { createApiHandler } from "@/utils/apiErrorHandler";
import { generateResumeHtml } from "@/utils/resumeHtml";

export interface ResumeRecord {
  id: string;
  user_id: string | null;
  data: any;
  status: "processing" | "done" | "error";
  pdf_url: string | null;
  created_at?: string;
  updated_at?: string;
}

async function handlePost(req: NextRequest) {
  const supabase = await createClient();
  const body = (await req.json()) as {
    data: any;
  };
  const id = uuidv4();

  // Generate HTML via OpenAI or fallback
  const html = await generateResumeHtml(body.data);

  const { error } = await supabase.from("resumes").insert({
    id,
    user_id: (await supabase.auth.getUser()).data.user?.id ?? null,
    data: body.data,
    html,
    status: "done",
    pdf_url: null,
  });

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ id }, { status: 201 });
}

export const POST = createApiHandler("resume-create", handlePost);
