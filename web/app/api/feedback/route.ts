export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { preventCaching } from '@/utils/cacheControl';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { feature, rating, comment, email } = body;
    
    // Validate required fields
    if (!feature || !rating) {
      return NextResponse.json(
        { success: false, error: 'Mohon lengkapi rating' },
        { status: 400 }
      );
    }
    
    // Validate rating is between 1-5
    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { success: false, error: 'Rating harus antara 1 sampai 5' },
        { status: 400 }
      );
    }
    
    // Create Supabase client
    const supabase = await createClient();
    
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');
    
    // Initialize user ID as null
    let userId = null;
    
    // If access token exists, get the user
    if (accessToken) {
      const { data: { user }, error: getUserError } = await supabase.auth.getUser(accessToken);
      
      if (!getUserError && user) {
        userId = user.id;
      }
    }
    
    // Insert feedback into database
    const { data, error } = await supabase
      .from('user_feedback')
      .insert([
        {
          user_id: userId, // Store user ID if logged in
          feature,
          rating,
          comment: comment || null,
          email: email || null,
          device_type: request.headers.get('user-agent') || 'unknown', // Store device info for analysis
          created_at: new Date().toISOString(),
        },
      ]);
    
    if (error) {
      console.error('Error storing feedback:', error);
      return NextResponse.json(
        { success: false, error: 'Gagal menyimpan feedback' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Feedback submission error:', error);
    return NextResponse.json(
      { success: false, error: 'Terjadi kesalahan saat memproses feedback' },
      { status: 500 }
    );
  }
}
