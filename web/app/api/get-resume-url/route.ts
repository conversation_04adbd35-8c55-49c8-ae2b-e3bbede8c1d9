export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import rollbar from '@/lib/rollbar';
import mixpanel from '@/lib/mixpanel';

export async function GET(request: NextRequest) {
  // Check if we want to download or just view the file
  const { searchParams } = new URL(request.url);
  const downloadMode = searchParams.get('download') === 'true';
  try {
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');
    
    if (!accessToken) {
      const response = NextResponse.json({ error: 'Missing authentication token' }, { status: 401 });
      response.headers.set("Netlify-Vary", "query");
      return response;
    }
    
    // Create Supabase client
    const supabase = await createClient();
    
    // Get user with the provided token
    const { data: { user }, error: getUserError } = await supabase.auth.getUser(accessToken);

    mixpanel.track('GET /api/get-resume-url', {
      endpoint: 'get-resume-url/GET',
      userId: user?.id,
      accessToken: accessToken
    });
    
    if (getUserError || !user) {
      console.error('Error getting user with token:', getUserError);
      const response = NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      response.headers.set("Netlify-Vary", "query");
      return response;
    }

    // Get user's resume filename from profiles table
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('resume_file_name, real_file_name')
      .eq('id', user.id)
      .single();
    
    if (profileError || !profile || !profile.resume_file_name) {
      if (profileError) {
        console.error('Profile error:', profileError);
        rollbar.logError(profileError, {
          endpoint: 'get-resume-url/GET',
          context: 'Error fetching profile data',
          userId: user?.id,
          errorDetails: JSON.stringify(profileError)
        });
      } else {
      }
      const response = NextResponse.json({ error: 'Resume tidak ditemukan' }, { status: 404 });
      response.headers.set("Netlify-Vary", "query");
      return response;
    }

    // Untuk download, kita gunakan opsi yang didukung oleh Supabase
    // download: true akan menghasilkan header Content-Disposition: attachment
    const options = {
      download: downloadMode ? profile.resume_file_name : undefined,
    };
    
    const { data: signedData, error: signedError } = await supabase
      .storage
      .from('resumes')
      .createSignedUrl(profile.resume_file_name, 3600, options);
    
    if (signedError || !signedData) {
      console.error('Error creating signed URL:', signedError);
      rollbar.logError(signedError || 'Failed to create signed URL (no error data)', {
        endpoint: 'get-resume-url/GET',
        context: 'Error creating signed URL for resume file',
        userId: user?.id,
        storageFilename: profile.resume_file_name,
        realFilename: profile.real_file_name,
        downloadMode: downloadMode,
        errorDetails: signedError ? JSON.stringify(signedError) : 'No signedData returned'
      });
      const response = NextResponse.json({ error: 'Gagal membuat URL untuk mengakses resume' }, { status: 500 });
      response.headers.set("Netlify-Vary", "query");
      return response;
    }

    const response = NextResponse.json({ 
      success: true,
      url: signedData.signedUrl
    });
    response.headers.set("Netlify-Vary", "query");
    return response;
    
  } catch (error) {
    console.error('Error in get-resume-url:', error);
    rollbar.logError(error instanceof Error ? error : 'Error in get-resume-url handler', {
      endpoint: 'get-resume-url/GET',
      context: 'Unhandled exception in GET handler',
      errorDetails: JSON.stringify(error, Object.getOwnPropertyNames(error))
    });
    const response = NextResponse.json({ error: 'Terjadi kesalahan server' }, { status: 500 });
    response.headers.set("Netlify-Vary", "query");
    return response;
  }
}
