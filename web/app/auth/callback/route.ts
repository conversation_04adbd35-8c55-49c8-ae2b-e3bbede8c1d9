export const dynamic = "force-dynamic";

import { NextResponse, type NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code');
  // if "next" is in param, use it as the redirect URL
  const next = searchParams.get('next') ?? '/'

  if (code) {
    const supabase = await createClient();
    const { data, error } = await supabase.auth.exchangeCodeForSession(code);

    // If authentication was successful and we have a user, ensure they have a profile record
    if (!error && data.user) {
      try {
        // Create or update profile record for the user (upsert with just the ID)
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert({
            id: data.user.id,
          }, {
            onConflict: 'id'
          });

        if (profileError) {
          console.error('Error creating/updating profile:', profileError);
          // Don't fail the auth flow if profile creation fails, just log it
        }
      } catch (profileError) {
        console.error('Exception during profile creation:', profileError);
        // Don't fail the auth flow if profile creation fails, just log it
      }
    }

    // Check if this is from OAuth popup (has opener window)
    // If so, return a page that closes the popup and notifies parent
    if (next && next !== '/') {
      const closePopupHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Login Successful</title>
        </head>
        <body>
          <script>
            // Check if this window was opened as a popup
            if (window.opener) {
              // Add a delay before closing the popup (2 seconds)
              setTimeout(() => {
                window.close();
              }, 2000);
            } else {
              // Regular redirect if not a popup
              window.location.href = '${next}';
            }
          </script>
          <p>Login berhasil! Jendela ini akan tertutup otomatis...</p>
        </body>
        </html>
      `;
      
      return new NextResponse(closePopupHtml, {
        headers: {
          'Content-Type': 'text/html',
          'Netlify-Vary': 'query'
        }
      });
    }

    const forwardedHost = request.headers.get('x-forwarded-host') // original origin before load balancer
    let response;
    if (forwardedHost) {
      response = NextResponse.redirect(`https://${forwardedHost}${next}`)
    } else {
      response = NextResponse.redirect(`${origin}${next}`)
    }
    response.headers.set("Netlify-Vary", "query");
    return response;
  }

  // URL to redirect to after sign in
  const response = NextResponse.redirect(new URL('/', origin));
  response.headers.set("Netlify-Vary", "query");
  return response;
}
