'use client';

import Link from 'next/link';
import NavbarMinimal from '@/components/NavbarMinimal';

export default function RegisterSuccess() {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Navigation Bar */}
      <NavbarMinimal />
      <div className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 space-y-8 text-center">
    {/* Gigsta Logo */}
    <div className="mx-auto mb-4 flex justify-center">
      <img src="/images/logo.svg" alt="Gigsta Logo" className="h-20 invert" />
    </div>
    <h2 className="text-3xl font-extrabold text-gray-900">Pendaftaran Berhasil!</h2>
    <div className="mt-6">
      <p className="text-gray-600 mb-4">
        <PERSON><PERSON><PERSON> perik<PERSON> email Anda untuk mengkonfirmasi pendaftaran. Anda akan menerima link konfirmasi untuk memverifikasi alamat email Anda. Periksa juga folder spam jika email tidak ditemukan.
      </p>
      <p className="text-gray-600">
        Setelah verifikasi, Anda dapat login ke akun Anda untuk mengakses semua fitur Gigsta.
      </p>
    </div>
    <div className="mt-8">
      <Link 
        href="/login" 
        className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark"
      >
        Kembali ke Halaman Login
      </Link>
    </div>
  </div>
</div>
    </div>
  );
}
